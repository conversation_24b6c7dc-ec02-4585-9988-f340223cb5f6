<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1>All files</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/5981</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/3557</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/609</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/5871</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th data-col="statements" data-type="number" data-fmt="pct" class="pct">
                Statements
              </th>
              <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
              <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
              <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
              <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file low" data-value="backend">
                <a href="backend/index.html">backend</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="280" class="abs low">0/280</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="42" class="abs low">0/42</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="29" class="abs low">0/29</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="274" class="abs low">0/274</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/config">
                <a href="backend/config/index.html">backend/config</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="21" class="abs low">0/21</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="19" class="abs low">0/19</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="20" class="abs low">0/20</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/controllers">
                <a href="backend/controllers/index.html">backend/controllers</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1014" class="abs low">0/1014</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="668" class="abs low">0/668</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="99" class="abs low">0/99</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1002" class="abs low">0/1002</td>
            </tr>

            <tr>
              <td class="file empty" data-value="backend/db">
                <a href="backend/db/index.html">backend/db</a>
              </td>
              <td data-value="0" class="pic empty">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct empty">0%</td>
              <td data-value="0" class="abs empty">0/0</td>
              <td data-value="0" class="pct empty">0%</td>
              <td data-value="0" class="abs empty">0/0</td>
              <td data-value="0" class="pct empty">0%</td>
              <td data-value="0" class="abs empty">0/0</td>
              <td data-value="0" class="pct empty">0%</td>
              <td data-value="0" class="abs empty">0/0</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/docs">
                <a href="backend/docs/index.html">backend/docs</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/errors">
                <a href="backend/errors/index.html">backend/errors</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="17" class="abs low">0/17</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="11" class="abs low">0/11</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="17" class="abs low">0/17</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/examples">
                <a href="backend/examples/index.html">backend/examples</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="670" class="abs low">0/670</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="148" class="abs low">0/148</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="41" class="abs low">0/41</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="658" class="abs low">0/658</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/logic">
                <a href="backend/logic/index.html">backend/logic</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="60" class="abs low">0/60</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="52" class="abs low">0/52</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="59" class="abs low">0/59</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/middleware">
                <a href="backend/middleware/index.html">backend/middleware</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="439" class="abs low">0/439</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="294" class="abs low">0/294</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="62" class="abs low">0/62</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="429" class="abs low">0/429</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/migrations">
                <a href="backend/migrations/index.html">backend/migrations</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="91" class="abs low">0/91</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="70" class="abs low">0/70</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="5" class="abs low">0/5</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="90" class="abs low">0/90</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/models">
                <a href="backend/models/index.html">backend/models</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="719" class="abs low">0/719</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="637" class="abs low">0/637</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="55" class="abs low">0/55</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="715" class="abs low">0/715</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/routes">
                <a href="backend/routes/index.html">backend/routes</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="473" class="abs low">0/473</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="178" class="abs low">0/178</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="63" class="abs low">0/63</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="466" class="abs low">0/466</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/scripts">
                <a href="backend/scripts/index.html">backend/scripts</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="205" class="abs low">0/205</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="23" class="abs low">0/23</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="205" class="abs low">0/205</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/seed">
                <a href="backend/seed/index.html">backend/seed</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="213" class="abs low">0/213</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="47" class="abs low">0/47</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="213" class="abs low">0/213</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/services">
                <a href="backend/services/index.html">backend/services</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="82" class="abs low">0/82</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="22" class="abs low">0/22</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="13" class="abs low">0/13</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="82" class="abs low">0/82</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/tests">
                <a href="backend/tests/index.html">backend/tests</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/tests/helpers">
                <a href="backend/tests/helpers/index.html">backend/tests/helpers</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="20" class="abs low">0/20</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="8" class="abs low">0/8</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="20" class="abs low">0/20</td>
            </tr>

            <tr>
              <td class="file low" data-value="backend/utils">
                <a href="backend/utils/index.html">backend/utils</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1666" class="abs low">0/1666</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1332" class="abs low">0/1332</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="196" class="abs low">0/196</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1610" class="abs low">0/1610</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="sorter.js"></script>
    <script src="block-navigation.js"></script>
  </body>
</html>
