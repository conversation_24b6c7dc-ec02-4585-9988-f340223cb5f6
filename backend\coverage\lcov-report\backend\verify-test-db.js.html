<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for backend/verify-test-db.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1>
          <a href="../index.html">All files</a> / <a href="index.html">backend</a> verify-test-db.js
        </h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/24</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/4</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">100% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/0</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/24</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// Verification script to ensure tests use the correct database
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
&nbsp;
const __filename = <span class="cstat-no" title="statement not covered" >fileURLToPath(import.meta.url);</span>
const __dirname = <span class="cstat-no" title="statement not covered" >dirname(__filename);</span>
&nbsp;
// Load test environment
<span class="cstat-no" title="statement not covered" >dotenv.config({ path: join(__dirname, '.env.test') });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >console.log('🔍 Database Configuration Verification');</span>
<span class="cstat-no" title="statement not covered" >console.log('=====================================');</span>
<span class="cstat-no" title="statement not covered" >console.log('NODE_ENV:', process.env.NODE_ENV);</span>
<span class="cstat-no" title="statement not covered" >console.log('DATABASE_URL:', process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@'));</span>
&nbsp;
// Check if using test database
<span class="cstat-no" title="statement not covered" >if (process.env.DATABASE_URL?.includes('equoria_test')) {</span>
<span class="cstat-no" title="statement not covered" >  console.log('✅ Correctly configured to use test database');</span>
} else {
<span class="cstat-no" title="statement not covered" >  console.log('❌ NOT using test database - this is a problem!');</span>
<span class="cstat-no" title="statement not covered" >  process.exit(1);</span>
}
&nbsp;
// Test database connection
import { Client } from 'pg';
&nbsp;
const client = <span class="cstat-no" title="statement not covered" >new Client({</span>
  connectionString: process.env.DATABASE_URL
});
&nbsp;
<span class="cstat-no" title="statement not covered" >try {</span>
<span class="cstat-no" title="statement not covered" >  await client.connect();</span>
  const result = <span class="cstat-no" title="statement not covered" >await client.query('SELECT current_database()');</span>
<span class="cstat-no" title="statement not covered" >  console.log('✅ Connected to database:', result.rows[0].current_database);</span>
  
<span class="cstat-no" title="statement not covered" >  if (result.rows[0].current_database === 'equoria_test') {</span>
<span class="cstat-no" title="statement not covered" >    console.log('✅ Confirmed: Using test database');</span>
  } else {
<span class="cstat-no" title="statement not covered" >    console.log('❌ ERROR: Connected to wrong database!');</span>
<span class="cstat-no" title="statement not covered" >    process.exit(1);</span>
  }
} catch (error) {
<span class="cstat-no" title="statement not covered" >  console.error('❌ Database connection failed:', error.message);</span>
<span class="cstat-no" title="statement not covered" >  process.exit(1);</span>
} finally {
<span class="cstat-no" title="statement not covered" >  await client.end();</span>
}
&nbsp;
<span class="cstat-no" title="statement not covered" >console.log('🎉 Test database configuration is correct!'); </span></pre></td></tr></table></pre>

      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../sorter.js"></script>
    <script src="../block-navigation.js"></script>
  </body>
</html>
