---
alwaysApply: true
---

!!Important!! You MUST use Sequential Thinking for every task
task: Document and audit the Equoria horse training system

## GROOMS SYSTEM – FULL IMPLEMENTATION TASKPLAN

### 🦜 System Overview

Grooms are hireable stable staff who assist with:
- Bonding with horses (bond score)
- Foal development (epigenetics, early training)
- Conformation show preparation and presentation
- Burnout recovery (through grooming and rest care)
- Parade event participation (cosmetic showcase roles)

Each groom has skill stats, can be assigned to horses, and influences gameplay systems that connect with the horse's emotional and developmental journey.

---

### 🔧 Backend Setup – PostgreSQL + Node

1. **Create a new `grooms` table** in PostgreSQL:
   - `id` (PK)
   - `name` (string)
   - `salary` (integer)
   - `bonding_skill` (integer)
   - `foal_training_skill` (integer)
   - `show_handling_skill` (integer)
   - `max_assignments` (integer)
   - `trait_perk` (optional string, nullable)
   - `is_active` (boolean)

2. **Create a new `horse_groom_assignments` table**:
   - `id` (PK)
   - `groom_id` (FK)
   - `horse_id` (FK)
   - `bond_score` (integer, default 0)
   - `assigned_at` (timestamp)

3. Add API routes for:
   - `GET /grooms` – List all hireable grooms
   - `POST /grooms/hire` – Hire a groom (with salary deduction)
   - `POST /grooms/assign` – Assign a groom to a horse
   - `POST /grooms/unassign` – Remove assignment
   - `GET /grooms/:id` – View groom profile and bonded horses
   - `PATCH /grooms/:id/skills` – Update groom skills (admin/testing)

---

### 🧠 Game Logic – Bonding, Salary, & System Effects

4. Create logic to:
   - Limit number of grooms based on `stable_level`
   - Enforce `max_assignments` per groom
   - Deduct groom salary weekly from player account
   - Increment `bond_score` daily for each assigned pair

5. Gameplay Integration:
   - **Bond Score Boosts**:
     - Affects competition performance and training effectiveness
     - Required for advanced foal development
     - Unlocks conformation and parade participation bonuses
   - **Foal Development**:
     - Grooms needed during imprinting, socialization, and fear periods
     - Influences epigenetic trait flags (e.g., brave, fearful)
     - Affects early bonding rate and trait formation
   - **Burnout Recovery**:
     - Horses in rest week recover faster with Groom support
     - Groom care applies a daily bonus to stress recovery
     - Pairs well with herbal supplement effects

---

### 🎖️ Conformation Show Mechanics

6. Grooms handle horses in conformation shows. Score calculation:
   - 60–70% = horse conformation stat ratings
   - 15–25% = groom’s show handling skill
   - 10–15% = bond score + temperament synergy

7. Groom must be assigned in advance and confirmed as show handler.
   - Bonus visuals/effects can be shown for well-matched pairs

---

### 🎨 Frontend UI – React Components

8. Build `GroomList` component:
   - Shows hireable grooms
   - Displays stats, salary, and available slots
   - Hire button (disabled if stable limit reached)

9. Build `MyGroomsDashboard`:
   - Lists all hired grooms and their assigned horses
   - Display bond levels, skills, and perk traits

10. Build `AssignGroomModal`:
   - Assign a groom to a horse (validate open slots)
   - Show skill stats and current bond score

11. Add weekly salary reminder on Dashboard:
   - “You paid $X in groom salaries this week.”
   - Notify player of unassigned grooms

---

### 🧲 Testing and Seed Data

12. Seed DB with 5 example grooms:
   - Varying names, salaries, and skills
   - Example names: “Beau”, “Mira”, “Samson”, “Lark”, “Ivy”

13. Unit Tests:
   - Hiring + salary logic
   - Groom assignment/unassignment
   - Bonding over time
   - Foal training trigger gates
   - Conformation scoring modifiers
   - Burnout recovery boosts

---