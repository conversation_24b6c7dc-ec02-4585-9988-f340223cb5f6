{"name": "equoria-monorepo", "version": "1.0.0", "type": "module", "description": "Root package for the Equoria project, enabling easier script execution for sub-projects.", "private": true, "scripts": {"test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watchAll", "test-backend": "cd backend && npm test", "test-backend:coverage": "cd backend && npm run test:coverage", "test-backend:watch": "cd backend && npm run test:watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "lint-backend": "cd backend && npm run lint", "lint:fix-backend": "cd backend && npm run lint:fix", "format-backend": "cd backend && npm run format", "dev-backend": "cd backend && npm run dev", "start-backend": "cd backend && npm run start"}, "keywords": ["equoria", "monorepo"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "fast-glob": "^3.3.2", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^22.15.30", "eslint": "^8.57.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.1", "jest": "^29.7.0", "jest-junit": "^16.0.0", "lodash": "^4.17.21", "nodemon": "^3.1.4", "prettier": "^3.3.2", "replace-in-files": "^3.0.0", "supertest": "^7.0.0", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7"}}