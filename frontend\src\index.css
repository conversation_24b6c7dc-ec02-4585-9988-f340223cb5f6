
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Equoria Fantasy Design System */

@layer base {
  :root {
    /* Fantasy Color Palette */
    --forest-green: 44 74 42;
    --aged-bronze: 106 78 45;
    --burnished-gold: 214 166 74;
    --saddle-leather: 139 94 60;
    --parchment: 253 248 227;
    --midnight-ink: 31 27 22;
    --mystic-silver: 176 179 184;
    
    /* Base System Colors */
    --background: var(--parchment);
    --foreground: var(--midnight-ink);
    
    --card: var(--parchment);
    --card-foreground: var(--midnight-ink);
    
    --popover: var(--parchment);
    --popover-foreground: var(--midnight-ink);
    
    --primary: var(--forest-green);
    --primary-foreground: var(--parchment);
    
    --secondary: var(--aged-bronze);
    --secondary-foreground: var(--parchment);
    
    --muted: var(--mystic-silver);
    --muted-foreground: var(--midnight-ink);
    
    --accent: var(--burnished-gold);
    --accent-foreground: var(--midnight-ink);
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: var(--parchment);
    
    --border: var(--aged-bronze);
    --input: var(--parchment);
    --ring: var(--burnished-gold);
    
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'Jost', sans-serif;
    background-image: 
      radial-gradient(circle at 20% 20%, rgba(214, 166, 74, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(106, 78, 45, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, rgba(253, 248, 227, 0.9) 0%, rgba(214, 166, 74, 0.1) 100%);
    background-attachment: fixed;
  }
}

/* Custom Typography Classes */
@layer components {
  .fantasy-title {
    font-family: 'Yeseva One', serif;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .fantasy-header {
    font-family: 'Cormorant Garamond', serif;
    font-weight: 600;
  }
  
  .fantasy-body {
    font-family: 'Jost', sans-serif;
    font-weight: 400;
  }
  
  .fantasy-caption {
    font-family: 'Jost', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-size: 0.75rem;
  }
}

/* Custom Component Styles */
@layer components {
  .parchment-texture {
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(214, 166, 74, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(106, 78, 45, 0.05) 0%, transparent 50%);
    position: relative;
  }
  
  .parchment-texture::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(214, 166, 74, 0.03) 2px,
        rgba(214, 166, 74, 0.03) 4px
      );
    pointer-events: none;
  }
  
  .gold-border {
    border: 2px solid rgb(var(--burnished-gold));
    position: relative;
  }
  
  .gold-border::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border: 1px solid rgb(var(--aged-bronze));
    border-radius: inherit;
    pointer-events: none;
  }
  
  .magical-glow {
    box-shadow: 
      0 0 10px rgba(214, 166, 74, 0.3),
      0 0 20px rgba(214, 166, 74, 0.1),
      inset 0 1px 0 rgba(214, 166, 74, 0.2);
  }
  
  .shimmer-effect {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(214, 166, 74, 0.4) 50%,
      transparent 100%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }
  
  .scroll-entrance {
    animation: scrollEntrance 0.6s ease-out;
  }
}

/* Animations */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes scrollEntrance {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes magicalPulse {
  0%, 100% {
    box-shadow: 
      0 0 10px rgba(214, 166, 74, 0.3),
      0 0 20px rgba(214, 166, 74, 0.1);
  }
  50% {
    box-shadow: 
      0 0 20px rgba(214, 166, 74, 0.5),
      0 0 40px rgba(214, 166, 74, 0.2);
  }
}

@keyframes sparkleTrail {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0);
  }
}

.magical-pulse {
  animation: magicalPulse 3s ease-in-out infinite;
}

.sparkle-trail {
  animation: sparkleTrail 1s ease-out;
}
