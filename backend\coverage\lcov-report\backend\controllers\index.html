<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for backend/controllers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1><a href="../../index.html">All files</a> backend/controllers</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/1014</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/668</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/99</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/1002</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th data-col="statements" data-type="number" data-fmt="pct" class="pct">
                Statements
              </th>
              <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
              <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
              <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
              <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file low" data-value="authController.js">
                <a href="authController.js.html">authController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="92" class="abs low">0/92</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="73" class="abs low">0/73</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="92" class="abs low">0/92</td>
            </tr>

            <tr>
              <td class="file low" data-value="breedController.js">
                <a href="breedController.js.html">breedController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="35" class="abs low">0/35</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="35" class="abs low">0/35</td>
            </tr>

            <tr>
              <td class="file low" data-value="competitionController.js">
                <a href="competitionController.js.html">competitionController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="143" class="abs low">0/143</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="107" class="abs low">0/107</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="16" class="abs low">0/16</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="140" class="abs low">0/140</td>
            </tr>

            <tr>
              <td class="file low" data-value="groomController.js">
                <a href="groomController.js.html">groomController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="81" class="abs low">0/81</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="73" class="abs low">0/73</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="81" class="abs low">0/81</td>
            </tr>

            <tr>
              <td class="file low" data-value="horseController.js">
                <a href="horseController.js.html">horseController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="121" class="abs low">0/121</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="81" class="abs low">0/81</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="120" class="abs low">0/120</td>
            </tr>

            <tr>
              <td class="file low" data-value="leaderboardController.js">
                <a href="leaderboardController.js.html">leaderboardController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="145" class="abs low">0/145</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="87" class="abs low">0/87</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="20" class="abs low">0/20</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="140" class="abs low">0/140</td>
            </tr>

            <tr>
              <td class="file low" data-value="pingController.js">
                <a href="pingController.js.html">pingController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
            </tr>

            <tr>
              <td class="file low" data-value="trainingController.js">
                <a href="trainingController.js.html">trainingController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="166" class="abs low">0/166</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="94" class="abs low">0/94</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="5" class="abs low">0/5</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="166" class="abs low">0/166</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitCompetitionController.js">
                <a href="traitCompetitionController.js.html">traitCompetitionController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="71" class="abs low">0/71</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="40" class="abs low">0/40</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="70" class="abs low">0/70</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitController.js">
                <a href="traitController.js.html">traitController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="87" class="abs low">0/87</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="77" class="abs low">0/77</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="10" class="abs low">0/10</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="86" class="abs low">0/86</td>
            </tr>

            <tr>
              <td class="file low" data-value="userController.js">
                <a href="userController.js.html">userController.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="55" class="abs low">0/55</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="12" class="abs low">0/12</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="54" class="abs low">0/54</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="../../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../../sorter.js"></script>
    <script src="../../block-navigation.js"></script>
  </body>
</html>
