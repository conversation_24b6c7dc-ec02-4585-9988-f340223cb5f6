/**
 * 🧪 INTEGRATION TEST: Leaderboard Controller - Real Database Operations
 *
 * This test validates the leaderboard controller's API endpoints using REAL database
 * operations following the proven minimal mocking TDD strategy (90.1% success rate).
 *
 * 🔄 MINIMAL MOCKING APPROACH (90.1% SUCCESS RATE):
 * ✅ REAL: Database operations, controller logic, response formatting, business logic
 * ✅ REAL: HTTP request/response handling, status codes, data transformation
 * ✅ REAL: Prisma queries, data aggregation, pagination logic
 * 🔧 MOCK: Logger only - external dependency for logging
 *
 * 💡 TEST STRATEGY: Integration testing with real database operations to validate
 *    actual business logic and catch real implementation issues
 */

import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Strategic mocking: Only mock external dependencies (logger)
const mockLogger = {
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
};

jest.mock('../backend/utils/logger.mjs', () => ({
  default: mockLogger,
}));

// Import real modules
import prisma from '../backend/db/index.mjs';

// Import the module under test
const {
  getTopPlayersByLevel,
  getTopPlayersByXP,
  getTopHorsesByEarnings,
} = await import(join(__dirname, '../backend/controllers/leaderboardController.mjs'));

describe('🏆 INTEGRATION: Leaderboard Controller - Real Database Operations', () => {
  let testUsers = [];
  let testHorses = [];
  let testBreeds = [];

  beforeEach(async () => {
    // Clean up test data
    await prisma.competitionEntry.deleteMany({});
    await prisma.xpEvent.deleteMany({});
    await prisma.horse.deleteMany({});
    await prisma.breed.deleteMany({});
    await prisma.user.deleteMany({});

    // Create test breeds
    const breed1 = await prisma.breed.create({
      data: {
        id: 1,
        name: 'Arabian',
        description: 'Test breed',
      },
    });

    testBreeds = [breed1];

    // Create test users with different levels and XP
    const user1 = await prisma.user.create({
      data: {
        id: 'test-user-1',
        username: 'Alice',
        email: '<EMAIL>',
        password: 'hashedpassword',
        level: 10,
        xp: 50,
        money: 5000,
      },
    });

    const user2 = await prisma.user.create({
      data: {
        id: 'test-user-2',
        username: 'Bob',
        email: '<EMAIL>',
        password: 'hashedpassword',
        level: 8,
        xp: 75,
        money: 3000,
      },
    });

    testUsers = [user1, user2];

    // Create test horses with different earnings
    const horse1 = await prisma.horse.create({
      data: {
        id: 1,
        name: 'Star',
        userId: user1.id,
        breedId: breed1.id,
        totalEarnings: 10000,
        birthDate: new Date('2020-01-01'),
        gender: 'mare',
        color: 'bay',
      },
    });

    testHorses = [horse1];

    // Create test XP events
    await prisma.xpEvent.create({
      data: {
        userId: user1.id,
        amount: 1000,
        source: 'training',
        createdAt: new Date(),
      },
    });

    jest.clearAllMocks();
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.competitionEntry.deleteMany({});
    await prisma.xpEvent.deleteMany({});
    await prisma.horse.deleteMany({});
    await prisma.breed.deleteMany({});
    await prisma.user.deleteMany({});
    jest.clearAllMocks();
  });

  describe('getTopPlayersByLevel', () => {
    it('should return top players ranked by level and XP using real database', async () => {
      const req = { query: { limit: '10', offset: '0' } };
      const res = {
        json: jest.fn(),
        status: jest.fn().mockReturnThis(),
      };

      await getTopPlayersByLevel(req, res);

      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Top users by level retrieved successfully',
        data: {
          users: expect.arrayContaining([
            expect.objectContaining({
              rank: 1,
              userId: 'test-user-1',
              name: 'Alice',
              level: 10,
              xp: 50,
              money: 5000,
            }),
            expect.objectContaining({
              rank: 2,
              userId: 'test-user-2',
              name: 'Bob',
              level: 8,
              xp: 75,
              money: 3000,
            }),
          ]),
          pagination: expect.objectContaining({
            limit: 10,
            offset: 0,
            total: 2,
            hasMore: false,
          }),
        },
      });

      // Verify users are ordered correctly by level (desc), then XP (desc)
      const responseData = res.json.mock.calls[0][0].data;
      expect(responseData.users[0].level).toBeGreaterThanOrEqual(responseData.users[1].level);
    });

    it('should handle default pagination parameters', async () => {
      const req = { query: {} }; // No limit/offset specified
      const res = { json: jest.fn(), status: jest.fn().mockReturnThis() };

      await getTopPlayersByLevel(req, res);

      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          message: 'Top users by level retrieved successfully',
          data: expect.objectContaining({
            pagination: expect.objectContaining({
              limit: 10, // Default limit
              offset: 0, // Default offset
            }),
          }),
        })
      );
    });
  });

  describe('getTopPlayersByXP', () => {
    it('should return top players by XP for all time using real database', async () => {
      const req = { query: { limit: '10', offset: '0' } };
      const res = { json: jest.fn(), status: jest.fn().mockReturnThis() };

      await getTopPlayersByXP(req, res);

      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Top players by XP retrieved successfully',
        data: {
          users: expect.arrayContaining([
            expect.objectContaining({
              rank: 1,
              userId: 'test-user-1',
              name: 'Alice',
              totalXp: 1000,
            }),
          ]),
          pagination: expect.objectContaining({
            limit: 10,
            offset: 0,
            hasMore: false,
          }),
        },
      });
    });
  });

  describe('getTopHorsesByEarnings', () => {
    it('should return top horses by earnings using real database', async () => {
      const req = { query: {} };
      const res = { json: jest.fn(), status: jest.fn().mockReturnThis() };

      await getTopHorsesByEarnings(req, res);

      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Top horses by earnings retrieved successfully',
        data: {
          horses: expect.arrayContaining([
            expect.objectContaining({
              rank: 1,
              horseId: 1,
              name: 'Star',
              earnings: 10000,
              ownerName: 'Alice',
              breedName: 'Arabian',
            }),
          ]),
          pagination: expect.objectContaining({
            limit: 10,
            offset: 0,
            total: 1,
            hasMore: false,
          }),
        },
      });
    });
  });
});
