# // DO NOT MODIFY: Configuration locked for consistency
# This file is used to configure the Equoria test environment.
# It contains settings for the test database, security, rate limiting,
# server configuration, and other test-specific settings.
# 🧪 EQUORIA TEST ENVIRONMENT CONFIGURATION

# ===== DATABASE CONFIGURATION =====
DATABASE_URL="postgresql://postgres:JimpkpNnVF2o%23DaX1Qx0@localhost:5432/equoria_test"

# ===== SECURITY CONFIGURATION =====
JWT_SECRET="test-jwt-secret-key-for-testing-only-32chars"
JWT_REFRESH_SECRET="test-jwt-refresh-secret-for-testing-only-32chars"
SESSION_SECRET="test-session-secret-for-testing-only-32chars"
BCRYPT_ROUNDS=10  # Minimum allowed value for tests

# ===== RATE LIMITING (Relaxed for testing) =====
RATE_LIMIT_WINDOW_MS=60000   # 1 minute
RATE_LIMIT_MAX_REQUESTS=1000 # Higher limit for tests
AUTH_RATE_LIMIT_MAX=50       # Higher limit for auth tests

# ===== SERVER CONFIGURATION =====
NODE_ENV=test
PORT=3001
LOG_LEVEL=error  # Reduce log noise during tests

# ===== CORS CONFIGURATION =====
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# ===== MONITORING & ALERTS (Disabled for testing) =====
ENABLE_AUDIT_LOGGING=false
ENABLE_SECURITY_ALERTS=false

# ===== GAME CONFIGURATION =====
TRAINING_COOLDOWN_DAYS=0  # No cooldowns in tests
BREEDING_COOLDOWN_DAYS=0  # No cooldowns in tests

# ===== JWT CONFIGURATION =====
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=2h

# ===== TEST SETTINGS =====
SKIP_AUTH_FOR_TESTING=true
ENABLE_DEBUG_ROUTES=true 