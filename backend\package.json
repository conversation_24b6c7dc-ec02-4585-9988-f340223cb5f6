{"name": "equoria-backend", "version": "1.0.0", "type": "module", "description": "Backend API for Equoria horse breeding and competition game", "main": "server.mjs", "scripts": {"start": "node server.mjs", "dev": "nodemon server.mjs", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watchAll", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "migrate": "node scripts/migrate.mjs", "seed": "node seed/seedDatabase.mjs"}, "keywords": ["equoria", "backend", "api", "horse", "breeding", "competition", "game"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "node-cron": "^4.2.1", "pg": "^8.16.0", "winston": "^3.11.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@jest/globals": "^29.7.0", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.4", "prettier": "^3.3.2", "supertest": "^7.0.0"}}