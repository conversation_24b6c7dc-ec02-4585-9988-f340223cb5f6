[project]
name = "mcp-server-mas-sequential-thinking"
version = "0.2.3"
description = "MCP Agent Implementation for Sequential Thinking"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    { name = "Frad LEE", email = "<EMAIL>" },
]
dependencies = [
    "agno",
    "asyncio",
    "exa-py",
    "python-dotenv",
    "mcp",
    "groq",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
    "isort",
    "mypy",
]

[project.scripts]
mcp-server-mas-sequential-thinking = "main:run"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
include = ["main.py"]
