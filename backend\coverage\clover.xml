<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748546394886" clover="3.2.0">
  <project timestamp="1748546394886" name="All files">
    <metrics statements="5871" coveredstatements="0" conditionals="3557" coveredconditionals="0" methods="609" coveredmethods="0" elements="10037" coveredelements="0" complexity="0" loc="5871" ncloc="5871" packages="18" files="110" classes="110"/>
    <package name="backend">
      <metrics statements="274" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="29" coveredmethods="0"/>
      <file name="app.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\app.js">
        <metrics statements="32" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
      </file>
      <file name="check-db.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\check-db.js">
        <metrics statements="15" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
      </file>
      <file name="eslint.config.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\eslint.config.js">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="fix-breed-relations.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\fix-breed-relations.js">
        <metrics statements="40" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
      </file>
      <file name="init-databases.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\init-databases.js">
        <metrics statements="15" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
      <file name="init-test-database.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\init-test-database.js">
        <metrics statements="29" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="seed-test-data.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\seed-test-data.js">
        <metrics statements="39" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
      </file>
      <file name="server.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\server.js">
        <metrics statements="31" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
      </file>
      <file name="test-connection-debug.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\test-connection-debug.js">
        <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
      </file>
      <file name="test-connection.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\test-connection.js">
        <metrics statements="26" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="test-postgres-connection.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\test-postgres-connection.js">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
      </file>
      <file name="verify-test-db.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\verify-test-db.js">
        <metrics statements="24" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.config">
      <metrics statements="20" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="config.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\config\config.js">
        <metrics statements="20" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.controllers">
      <metrics statements="1002" coveredstatements="0" conditionals="668" coveredconditionals="0" methods="99" coveredmethods="0"/>
      <file name="authController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\authController.js">
        <metrics statements="92" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="314" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
      </file>
      <file name="breedController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\breedController.js">
        <metrics statements="35" coveredstatements="0" conditionals="18" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
      <file name="competitionController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\competitionController.js">
        <metrics statements="140" coveredstatements="0" conditionals="107" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="26" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="358" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="402" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="415" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="446" count="0" type="stmt"/>
      </file>
      <file name="groomController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\groomController.js">
        <metrics statements="81" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="32" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="72" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="113" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="177" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="stmt"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="352" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="361" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="379" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
      </file>
      <file name="horseController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\horseController.js">
        <metrics statements="120" coveredstatements="0" conditionals="81" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="85" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="216" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="265" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="311" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="347" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="417" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
      </file>
      <file name="leaderboardController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\leaderboardController.js">
        <metrics statements="140" coveredstatements="0" conditionals="87" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="337" count="0" type="stmt"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="340" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="457" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="458" count="0" type="stmt"/>
        <line num="459" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="466" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="467" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="544" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="589" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
      </file>
      <file name="pingController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\pingController.js">
        <metrics statements="18" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
      </file>
      <file name="trainingController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\trainingController.js">
        <metrics statements="166" coveredstatements="0" conditionals="94" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="399" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="400" count="0" type="stmt"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="406" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="445" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="446" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="449" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="453" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="469" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
      </file>
      <file name="traitCompetitionController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\traitCompetitionController.js">
        <metrics statements="70" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="131" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="266" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
      </file>
      <file name="traitController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\traitController.js">
        <metrics statements="86" coveredstatements="0" conditionals="77" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="224" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="305" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="313" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
      </file>
      <file name="userController.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\controllers\userController.js">
        <metrics statements="54" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.db">
      <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\db\index.js">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="backend.docs">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="swagger.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\docs\swagger.js">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.errors">
      <metrics statements="17" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="AppError.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\errors\AppError.js">
        <metrics statements="5" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
      </file>
      <file name="DatabaseError.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\errors\DatabaseError.js">
        <metrics statements="3" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
      </file>
      <file name="NotFoundError.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\errors\NotFoundError.js">
        <metrics statements="5" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="ValidationError.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\errors\ValidationError.js">
        <metrics statements="4" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\errors\index.js">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="backend.examples">
      <metrics statements="658" coveredstatements="0" conditionals="148" coveredconditionals="0" methods="41" coveredmethods="0"/>
      <file name="applyEpigeneticTraitsAtBirthExample.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\applyEpigeneticTraitsAtBirthExample.js">
        <metrics statements="65" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
      </file>
      <file name="atBirthTraitsExample.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\atBirthTraitsExample.js">
        <metrics statements="104" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
      </file>
      <file name="epigeneticTraitsExample.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\epigeneticTraitsExample.js">
        <metrics statements="120" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
      </file>
      <file name="foalEnrichmentDemo.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\foalEnrichmentDemo.js">
        <metrics statements="62" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
      </file>
      <file name="horseTraitHelperUsage.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\horseTraitHelperUsage.js">
        <metrics statements="126" coveredstatements="0" conditionals="49" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
      </file>
      <file name="lineageTraitCheckExample.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\lineageTraitCheckExample.js">
        <metrics statements="104" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
      </file>
      <file name="traitIntegrationExample.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\examples\traitIntegrationExample.js">
        <metrics statements="77" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.logic">
      <metrics statements="59" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="simulateCompetition.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\logic\simulateCompetition.js">
        <metrics statements="59" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.middleware">
      <metrics statements="429" coveredstatements="0" conditionals="294" coveredconditionals="0" methods="62" coveredmethods="0"/>
      <file name="auditLog.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\auditLog.js">
        <metrics statements="87" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="252" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
      </file>
      <file name="auth.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\auth.js">
        <metrics statements="64" coveredstatements="0" conditionals="35" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
      </file>
      <file name="errorHandler.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\errorHandler.js">
        <metrics statements="20" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="gameIntegrity.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\gameIntegrity.js">
        <metrics statements="133" coveredstatements="0" conditionals="104" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
      </file>
      <file name="requestLogger.mjs" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\requestLogger.mjs">
        <metrics statements="12" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
      <file name="security.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\security.js">
        <metrics statements="19" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
      <file name="traitDiscoveryMiddleware.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\traitDiscoveryMiddleware.js">
        <metrics statements="83" coveredstatements="0" conditionals="75" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="96" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="212" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
      </file>
      <file name="validateHorse.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\validateHorse.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
      </file>
      <file name="validatePing.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\validatePing.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
      </file>
      <file name="validationErrorHandler.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\middleware\validationErrorHandler.js">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.migrations">
      <metrics statements="90" coveredstatements="0" conditionals="70" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="20250527_fixed_user_schema.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\migrations\20250527_fixed_user_schema.js">
        <metrics statements="48" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
      </file>
      <file name="20250527_update_user_schema.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\migrations\20250527_update_user_schema.js">
        <metrics statements="42" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.models">
      <metrics statements="715" coveredstatements="0" conditionals="637" coveredconditionals="0" methods="55" coveredmethods="0"/>
      <file name="foalModel.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\models\foalModel.js">
        <metrics statements="107" coveredstatements="0" conditionals="80" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="314" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="395" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="399" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="418" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
      </file>
      <file name="horseModel.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\models\horseModel.js">
        <metrics statements="273" coveredstatements="0" conditionals="321" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="294" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="stmt"/>
        <line num="298" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="349" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="362" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="363" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="367" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="403" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="416" count="0" type="stmt"/>
        <line num="419" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="420" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="422" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="445" count="0" type="stmt"/>
        <line num="448" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="449" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="461" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="462" count="0" type="stmt"/>
        <line num="465" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="467" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="468" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="469" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="471" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="501" count="0" type="stmt"/>
        <line num="504" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="505" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="509" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="510" count="0" type="stmt"/>
        <line num="514" count="0" type="stmt"/>
        <line num="523" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="524" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="529" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="540" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="541" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="542" count="0" type="stmt"/>
        <line num="545" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="546" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="587" count="0" type="stmt"/>
        <line num="588" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="592" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="593" count="0" type="stmt"/>
        <line num="596" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="597" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="610" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="611" count="0" type="stmt"/>
        <line num="615" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="618" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="619" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="623" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="624" count="0" type="stmt"/>
        <line num="626" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="627" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="632" count="0" type="stmt"/>
        <line num="645" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="650" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="662" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="666" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="667" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="679" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="680" count="0" type="stmt"/>
        <line num="683" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="686" count="0" type="stmt"/>
        <line num="693" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="699" count="0" type="stmt"/>
        <line num="714" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="718" count="0" type="stmt"/>
        <line num="719" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="720" count="0" type="stmt"/>
        <line num="723" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="724" count="0" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="736" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="737" count="0" type="stmt"/>
        <line num="740" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="742" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="743" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="744" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="746" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="748" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="750" count="0" type="stmt"/>
        <line num="753" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="764" count="0" type="stmt"/>
        <line num="765" count="0" type="stmt"/>
        <line num="768" count="0" type="stmt"/>
        <line num="769" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="770" count="0" type="stmt"/>
        <line num="773" count="0" type="stmt"/>
        <line num="782" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="783" count="0" type="stmt"/>
        <line num="786" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="787" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="789" count="0" type="stmt"/>
        <line num="791" count="0" type="stmt"/>
        <line num="794" count="0" type="stmt"/>
        <line num="795" count="0" type="stmt"/>
        <line num="805" count="0" type="stmt"/>
        <line num="806" count="0" type="stmt"/>
        <line num="809" count="0" type="stmt"/>
        <line num="810" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="811" count="0" type="stmt"/>
        <line num="814" count="0" type="stmt"/>
        <line num="823" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="824" count="0" type="stmt"/>
        <line num="827" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="828" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="830" count="0" type="stmt"/>
        <line num="832" count="0" type="stmt"/>
        <line num="835" count="0" type="stmt"/>
        <line num="836" count="0" type="stmt"/>
        <line num="848" count="0" type="stmt"/>
        <line num="849" count="0" type="stmt"/>
        <line num="852" count="0" type="stmt"/>
        <line num="853" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="854" count="0" type="stmt"/>
        <line num="857" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="858" count="0" type="stmt"/>
        <line num="862" count="0" type="stmt"/>
        <line num="863" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="864" count="0" type="stmt"/>
        <line num="868" count="0" type="stmt"/>
        <line num="870" count="0" type="stmt"/>
        <line num="872" count="0" type="stmt"/>
        <line num="875" count="0" type="stmt"/>
        <line num="876" count="0" type="stmt"/>
      </file>
      <file name="resultModel.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\models\resultModel.js">
        <metrics statements="53" coveredstatements="0" conditionals="48" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
      </file>
      <file name="trainingModel.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\models\trainingModel.js">
        <metrics statements="58" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
      </file>
      <file name="userModel.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\models\userModel.js">
        <metrics statements="161" coveredstatements="0" conditionals="124" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="173" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="299" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="399" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="400" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="404" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
      </file>
      <file name="xpLogModel.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\models\xpLogModel.js">
        <metrics statements="63" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.routes">
      <metrics statements="466" coveredstatements="0" conditionals="178" coveredconditionals="0" methods="63" coveredmethods="0"/>
      <file name="adminRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\adminRoutes.js">
        <metrics statements="44" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
      </file>
      <file name="authRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\authRoutes.js">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
      </file>
      <file name="breedRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\breedRoutes.js">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
      </file>
      <file name="competitionRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\competitionRoutes.js">
        <metrics statements="42" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="149" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
      </file>
      <file name="foalRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\foalRoutes.js">
        <metrics statements="65" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="100" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="107" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="224" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="231" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
      </file>
      <file name="groomRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\groomRoutes.js">
        <metrics statements="14" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="19" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
      </file>
      <file name="horseRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\horseRoutes.js">
        <metrics statements="36" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
      </file>
      <file name="leaderboardRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\leaderboardRoutes.js">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
      </file>
      <file name="ping.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\ping.js">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
      </file>
      <file name="trainingRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\trainingRoutes.js">
        <metrics statements="45" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
      </file>
      <file name="traitDiscoveryRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\traitDiscoveryRoutes.js">
        <metrics statements="83" coveredstatements="0" conditionals="36" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="283" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="313" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
      </file>
      <file name="traitRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\traitRoutes.js">
        <metrics statements="18" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
      </file>
      <file name="userRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\userRoutes.js">
        <metrics statements="15" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
      </file>
      <file name="xpRoutes.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\routes\xpRoutes.js">
        <metrics statements="83" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="169" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.scripts">
      <metrics statements="205" coveredstatements="0" conditionals="23" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="migrate.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\scripts\migrate.js">
        <metrics statements="19" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
      <file name="setup-test-db.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\scripts\setup-test-db.js">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
      </file>
      <file name="testUserProgressAPI.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\scripts\testUserProgressAPI.js">
        <metrics statements="50" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
      </file>
      <file name="testXpSystem.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\scripts\testXpSystem.js">
        <metrics statements="59" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
      </file>
      <file name="verifyUserSchema.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\scripts\verifyUserSchema.js">
        <metrics statements="65" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.seed">
      <metrics statements="213" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="horseSeed.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\seed\horseSeed.js">
        <metrics statements="124" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="338" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="404" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="405" count="0" type="stmt"/>
      </file>
      <file name="seedShows.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\seed\seedShows.js">
        <metrics statements="59" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
      </file>
      <file name="userSeed.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\seed\userSeed.js">
        <metrics statements="30" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.services">
      <metrics statements="82" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="13" coveredmethods="0"/>
      <file name="cronJobs.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\services\cronJobs.js">
        <metrics statements="82" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.tests">
      <metrics statements="9" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="setup.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\tests\setup.js">
        <metrics statements="9" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.tests.helpers">
      <metrics statements="20" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="7" coveredmethods="0"/>
      <file name="authHelper.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\tests\helpers\authHelper.js">
        <metrics statements="12" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="8" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="44" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="testAuth.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\tests\helpers\testAuth.js">
        <metrics statements="8" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
    </package>
    <package name="backend.utils">
      <metrics statements="1610" coveredstatements="0" conditionals="1332" coveredconditionals="0" methods="196" coveredmethods="0"/>
      <file name="apiResponse.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\apiResponse.js">
        <metrics statements="49" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
      </file>
      <file name="applyEpigeneticTraitsAtBirth.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\applyEpigeneticTraitsAtBirth.js">
        <metrics statements="106" coveredstatements="0" conditionals="112" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="196" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="250" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
      </file>
      <file name="atBirthTraits.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\atBirthTraits.js">
        <metrics statements="209" coveredstatements="0" conditionals="129" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="311" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="366" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="367" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="376" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="392" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="407" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="417" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="418" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="433" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="434" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="468" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="470" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="471" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="474" count="0" type="stmt"/>
        <line num="476" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="477" count="0" type="stmt"/>
        <line num="479" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="480" count="0" type="stmt"/>
        <line num="482" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="483" count="0" type="stmt"/>
        <line num="485" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="486" count="0" type="stmt"/>
        <line num="488" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="489" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="504" count="0" type="stmt"/>
        <line num="506" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="507" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="520" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="521" count="0" type="stmt"/>
        <line num="524" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="525" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="529" count="0" type="stmt"/>
        <line num="532" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="533" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="539" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="546" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="547" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="569" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="580" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="581" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
        <line num="586" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="587" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="591" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="594" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="595" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="600" count="0" type="stmt"/>
        <line num="609" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="610" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="616" count="0" type="stmt"/>
        <line num="617" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="623" count="0" type="stmt"/>
      </file>
      <file name="bondingModifiers.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\bondingModifiers.js">
        <metrics statements="104" coveredstatements="0" conditionals="87" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="302" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
      </file>
      <file name="competitionRewards.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\competitionRewards.js">
        <metrics statements="13" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="3"/>
      </file>
      <file name="competitionScore.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\competitionScore.js">
        <metrics statements="48" coveredstatements="0" conditionals="73" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="dailyCareAutomation.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\dailyCareAutomation.js">
        <metrics statements="76" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="351" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
      </file>
      <file name="epigeneticTraits.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\epigeneticTraits.js">
        <metrics statements="101" coveredstatements="0" conditionals="88" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="80" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="231" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="291" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
      </file>
      <file name="generateMockShows.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\generateMockShows.js">
        <metrics statements="24" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
      </file>
      <file name="getStatScore.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\getStatScore.js">
        <metrics statements="15" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
      </file>
      <file name="groomSystem.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\groomSystem.js">
        <metrics statements="76" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="376" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="390" count="0" type="stmt"/>
        <line num="391" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="392" count="0" type="stmt"/>
        <line num="393" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="394" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
      </file>
      <file name="healthBonus.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\healthBonus.js">
        <metrics statements="2" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="healthCheck.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\healthCheck.js">
        <metrics statements="49" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
      </file>
      <file name="horseUpdates.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\horseUpdates.js">
        <metrics statements="33" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
      <file name="isHorseEligible.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\isHorseEligible.js">
        <metrics statements="17" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
      </file>
      <file name="lineageTraitCheck.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\lineageTraitCheck.js">
        <metrics statements="88" coveredstatements="0" conditionals="70" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="167" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="225" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
      </file>
      <file name="logger.mjs" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\logger.mjs">
        <metrics statements="6" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
      <file name="riderBonus.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\riderBonus.js">
        <metrics statements="7" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="19" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
      </file>
      <file name="securityValidation.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\securityValidation.js">
        <metrics statements="191" coveredstatements="0" conditionals="171" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="333" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="379" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="385" count="0" type="stmt"/>
        <line num="389" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="390" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="391" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
      </file>
      <file name="statMap.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\statMap.js">
        <metrics statements="6" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="temperamentDrift.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\temperamentDrift.js">
        <metrics statements="68" coveredstatements="0" conditionals="76" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="199" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="213" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="trainingCooldown.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\trainingCooldown.js">
        <metrics statements="39" coveredstatements="0" conditionals="28" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
      </file>
      <file name="traitCompetitionImpact.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\traitCompetitionImpact.js">
        <metrics statements="54" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="362" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="3"/>
      </file>
      <file name="traitDiscovery.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\traitDiscovery.js">
        <metrics statements="94" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="21" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="224" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="300" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="303" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="370" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="traitEffects.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\traitEffects.js">
        <metrics statements="36" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="619" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="625" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="626" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="630" count="0" type="stmt"/>
        <line num="638" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="649" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="658" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="659" count="0" type="stmt"/>
        <line num="660" count="0" type="stmt"/>
        <line num="663" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="666" count="0" type="stmt"/>
        <line num="667" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="669" count="0" type="stmt"/>
        <line num="670" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="671" count="0" type="stmt"/>
        <line num="674" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="676" count="0" type="stmt"/>
        <line num="677" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="679" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="680" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="682" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="684" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="686" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="687" count="0" type="stmt"/>
        <line num="689" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="694" count="0" type="stmt"/>
        <line num="702" count="0" type="stmt"/>
      </file>
      <file name="traitEvaluation.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\traitEvaluation.js">
        <metrics statements="76" coveredstatements="0" conditionals="82" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="318" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="323" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="340" count="0" type="stmt"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="358" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="364" count="0" type="stmt"/>
        <line num="368" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="369" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
      </file>
      <file name="userUpdates.js" path="C:\Users\<USER>\OneDrive\Desktop\Equoria\backend\utils\userUpdates.js">
        <metrics statements="23" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
