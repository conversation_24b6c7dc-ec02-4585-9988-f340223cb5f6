<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for backend/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1><a href="../../index.html">All files</a> backend/utils</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/1666</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/1332</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/196</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/1610</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th data-col="statements" data-type="number" data-fmt="pct" class="pct">
                Statements
              </th>
              <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
              <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
              <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
              <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file low" data-value="apiResponse.js">
                <a href="apiResponse.js.html">apiResponse.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="51" class="abs low">0/51</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="44" class="abs low">0/44</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="23" class="abs low">0/23</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="49" class="abs low">0/49</td>
            </tr>

            <tr>
              <td class="file low" data-value="applyEpigeneticTraitsAtBirth.js">
                <a href="applyEpigeneticTraitsAtBirth.js.html">applyEpigeneticTraitsAtBirth.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="106" class="abs low">0/106</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="112" class="abs low">0/112</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="8" class="abs low">0/8</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="106" class="abs low">0/106</td>
            </tr>

            <tr>
              <td class="file low" data-value="atBirthTraits.js">
                <a href="atBirthTraits.js.html">atBirthTraits.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="223" class="abs low">0/223</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="129" class="abs low">0/129</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="209" class="abs low">0/209</td>
            </tr>

            <tr>
              <td class="file low" data-value="bondingModifiers.js">
                <a href="bondingModifiers.js.html">bondingModifiers.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="104" class="abs low">0/104</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="87" class="abs low">0/87</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="104" class="abs low">0/104</td>
            </tr>

            <tr>
              <td class="file low" data-value="competitionRewards.js">
                <a href="competitionRewards.js.html">competitionRewards.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="13" class="abs low">0/13</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="5" class="abs low">0/5</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="13" class="abs low">0/13</td>
            </tr>

            <tr>
              <td class="file low" data-value="competitionScore.js">
                <a href="competitionScore.js.html">competitionScore.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="48" class="abs low">0/48</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="73" class="abs low">0/73</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="5" class="abs low">0/5</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="48" class="abs low">0/48</td>
            </tr>

            <tr>
              <td class="file low" data-value="dailyCareAutomation.js">
                <a href="dailyCareAutomation.js.html">dailyCareAutomation.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="81" class="abs low">0/81</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="33" class="abs low">0/33</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="11" class="abs low">0/11</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="76" class="abs low">0/76</td>
            </tr>

            <tr>
              <td class="file low" data-value="epigeneticTraits.js">
                <a href="epigeneticTraits.js.html">epigeneticTraits.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="110" class="abs low">0/110</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="88" class="abs low">0/88</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="14" class="abs low">0/14</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="101" class="abs low">0/101</td>
            </tr>

            <tr>
              <td class="file low" data-value="generateMockShows.js">
                <a href="generateMockShows.js.html">generateMockShows.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="25" class="abs low">0/25</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="24" class="abs low">0/24</td>
            </tr>

            <tr>
              <td class="file low" data-value="getStatScore.js">
                <a href="getStatScore.js.html">getStatScore.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="15" class="abs low">0/15</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="17" class="abs low">0/17</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1" class="abs low">0/1</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="15" class="abs low">0/15</td>
            </tr>

            <tr>
              <td class="file low" data-value="groomSystem.js">
                <a href="groomSystem.js.html">groomSystem.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="76" class="abs low">0/76</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="32" class="abs low">0/32</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="76" class="abs low">0/76</td>
            </tr>

            <tr>
              <td class="file low" data-value="healthBonus.js">
                <a href="healthBonus.js.html">healthBonus.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1" class="abs low">0/1</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
            </tr>

            <tr>
              <td class="file low" data-value="healthCheck.js">
                <a href="healthCheck.js.html">healthCheck.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="51" class="abs low">0/51</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="26" class="abs low">0/26</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="10" class="abs low">0/10</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="49" class="abs low">0/49</td>
            </tr>

            <tr>
              <td class="file low" data-value="horseUpdates.js">
                <a href="horseUpdates.js.html">horseUpdates.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="33" class="abs low">0/33</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="33" class="abs low">0/33</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="33" class="abs low">0/33</td>
            </tr>

            <tr>
              <td class="file low" data-value="isHorseEligible.js">
                <a href="isHorseEligible.js.html">isHorseEligible.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="17" class="abs low">0/17</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="24" class="abs low">0/24</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1" class="abs low">0/1</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="17" class="abs low">0/17</td>
            </tr>

            <tr>
              <td class="file low" data-value="lineageTraitCheck.js">
                <a href="lineageTraitCheck.js.html">lineageTraitCheck.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="88" class="abs low">0/88</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="70" class="abs low">0/70</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="88" class="abs low">0/88</td>
            </tr>

            <tr>
              <td class="file low" data-value="logger.mjs">
                <a href="logger.mjs.html">logger.mjs</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
            </tr>

            <tr>
              <td class="file low" data-value="riderBonus.js">
                <a href="riderBonus.js.html">riderBonus.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="16" class="abs low">0/16</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1" class="abs low">0/1</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
            </tr>

            <tr>
              <td class="file low" data-value="securityValidation.js">
                <a href="securityValidation.js.html">securityValidation.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="193" class="abs low">0/193</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="171" class="abs low">0/171</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="12" class="abs low">0/12</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="191" class="abs low">0/191</td>
            </tr>

            <tr>
              <td class="file low" data-value="statMap.js">
                <a href="statMap.js.html">statMap.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
            </tr>

            <tr>
              <td class="file low" data-value="temperamentDrift.js">
                <a href="temperamentDrift.js.html">temperamentDrift.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="70" class="abs low">0/70</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="76" class="abs low">0/76</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="9" class="abs low">0/9</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="68" class="abs low">0/68</td>
            </tr>

            <tr>
              <td class="file low" data-value="trainingCooldown.js">
                <a href="trainingCooldown.js.html">trainingCooldown.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="39" class="abs low">0/39</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="28" class="abs low">0/28</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="39" class="abs low">0/39</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitCompetitionImpact.js">
                <a href="traitCompetitionImpact.js.html">traitCompetitionImpact.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="59" class="abs low">0/59</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="43" class="abs low">0/43</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="54" class="abs low">0/54</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitDiscovery.js">
                <a href="traitDiscovery.js.html">traitDiscovery.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="104" class="abs low">0/104</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="79" class="abs low">0/79</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="21" class="abs low">0/21</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="94" class="abs low">0/94</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitEffects.js">
                <a href="traitEffects.js.html">traitEffects.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="36" class="abs low">0/36</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="34" class="abs low">0/34</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="36" class="abs low">0/36</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitEvaluation.js">
                <a href="traitEvaluation.js.html">traitEvaluation.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="80" class="abs low">0/80</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="82" class="abs low">0/82</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="76" class="abs low">0/76</td>
            </tr>

            <tr>
              <td class="file low" data-value="userUpdates.js">
                <a href="userUpdates.js.html">userUpdates.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="23" class="abs low">0/23</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="10" class="abs low">0/10</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="23" class="abs low">0/23</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="../../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../../sorter.js"></script>
    <script src="../../block-navigation.js"></script>
  </body>
</html>
