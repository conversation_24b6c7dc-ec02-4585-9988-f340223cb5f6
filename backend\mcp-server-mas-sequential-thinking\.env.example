# --- LLM Configuration ---
# Select the LLM provider: "deepseek" (default), "groq", or "openrouter"
LLM_PROVIDER="deepseek"

# Provide the API key for the chosen provider:
# GROQ_API_KEY="your_groq_api_key"
DEEPSEEK_API_KEY="your_deepseek_api_key"
# OPENROUTER_API_KEY="your_openrouter_api_key"

# Optional: Base URL override (e.g., for custom DeepSeek endpoints)
DEEPSEEK_BASE_URL="your_base_url_if_needed"

# Optional: Specify different models for Team Coordinator and Specialist Agents
# Defaults are set within the code based on the provider if these are not set.
# Example for Groq:
# GROQ_TEAM_MODEL_ID="llama3-70b-8192"
# GROQ_AGENT_MODEL_ID="llama3-8b-8192"
# Example for DeepSeek:
# DEEPSEEK_TEAM_MODEL_ID="deepseek-chat"
# DEEPSEEK_AGENT_MODEL_ID="deepseek-coder"
# Example for OpenRouter:
# OPENROUTER_TEAM_MODEL_ID="anthropic/claude-3-haiku-20240307"
# OPENROUTER_AGENT_MODEL_ID="google/gemini-flash-1.5"

# --- External Tools ---
# Required ONLY if the Researcher agent is used and needs Exa
EXA_API_KEY="your_exa_api_key"