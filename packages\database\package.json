{"name": "database", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "lint:fix": "eslint . --fix", "generate": "npx prisma generate --schema=prisma/schema.prisma", "studio": "npx prisma studio --schema=prisma/schema.prisma", "migrate:dev": "npx prisma migrate dev --schema=prisma/schema.prisma", "migrate:reset": "npx prisma migrate reset --schema=prisma/schema.prisma", "migrate:deploy": "npx prisma migrate deploy --schema=prisma/schema.prisma", "migrate:status": "npx prisma migrate status --schema=prisma/schema.prisma"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^16.3.1"}, "devDependencies": {"@prisma/client": "^6.8.2", "prisma": "^6.8.2"}, "rules": {"no-console": "off"}, "prisma": {"schema": "prisma/schema.prisma"}}