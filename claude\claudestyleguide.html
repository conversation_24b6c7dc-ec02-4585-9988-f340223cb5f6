<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Equoria Style Guide & Design System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8faf7;
            line-height: 1.6;
            color: #2d3748;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #2C5530, #4A7C59);
            color: white;
            padding: 40px;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: "🐎";
            position: absolute;
            top: -20px;
            right: -20px;
            font-size: 120px;
            opacity: 0.1;
            transform: rotate(15deg);
        }
        
        .header h1 {
            font-size: 3rem;
            font-family: 'Crimson Text', serif;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            border-radius: 16px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 8px 32px rgba(44, 85, 48, 0.1);
            border: 1px solid #e8f5e8;
        }
        
        .section-title {
            font-size: 2rem;
            font-family: 'Crimson Text', serif;
            color: #2C5530;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .section-title::before {
            content: "🏇";
            font-size: 1.5rem;
        }
        
        /* Color Palette */
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .color-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .color-card:hover {
            transform: translateY(-4px);
        }
        
        .color-swatch {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .color-info {
            padding: 20px;
            background: white;
        }
        
        .color-name {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 8px;
        }
        
        .color-values {
            font-family: 'Monaco', monospace;
            font-size: 0.9rem;
            color: #666;
        }
        
        .color-usage {
            font-size: 0.85rem;
            color: #888;
            margin-top: 8px;
            font-style: italic;
        }
        
        /* Typography */
        .type-specimen {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8faf7;
            border-radius: 12px;
            border-left: 4px solid #2C5530;
        }
        
        .font-name {
            font-size: 0.9rem;
            color: #666;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        /* Components */
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .component {
            background: #f8faf7;
            padding: 30px;
            border-radius: 12px;
            border: 2px solid #e8f5e8;
        }
        
        .component-title {
            font-weight: bold;
            color: #2C5530;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        
        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.95rem;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #DAA520, #B8860B);
            color: white;
            box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(218, 165, 32, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #2C5530, #4A7C59);
            color: white;
            box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 85, 48, 0.4);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #DAA520;
            color: #DAA520;
        }
        
        .btn-outline:hover {
            background: #DAA520;
            color: white;
        }
        
        /* Card Styles */
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(44, 85, 48, 0.08);
            border: 1px solid #e8f5e8;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 48px rgba(44, 85, 48, 0.15);
        }
        
        .horse-card {
            text-align: center;
        }
        
        .horse-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
        }
        
        .horse-name {
            font-family: 'Crimson Text', serif;
            font-size: 1.3rem;
            font-weight: bold;
            color: #2C5530;
            margin-bottom: 4px;
        }
        
        .horse-breed {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 16px;
        }
        
        /* Progress Bars */
        .progress-bar {
            height: 8px;
            background: #e8f5e8;
            border-radius: 20px;
            overflow: hidden;
            margin: 8px 0;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #DAA520, #FFD700);
            border-radius: 20px;
            transition: width 0.6s ease;
            position: relative;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* Badges and Pills */
        .badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-level {
            background: linear-gradient(135deg, #DAA520, #FFD700);
            color: white;
        }
        
        .badge-breed {
            background: #e8f5e8;
            color: #2C5530;
        }
        
        .badge-status {
            background: #dcfce7;
            color: #166534;
        }
        
        /* Navigation */
        .nav-demo {
            background: white;
            border-radius: 20px;
            padding: 8px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 4px 20px rgba(44, 85, 48, 0.15);
            border: 1px solid #e8f5e8;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #666;
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, #2C5530, #4A7C59);
            color: white;
            transform: scale(1.05);
        }
        
        .nav-icon {
            font-size: 1.2rem;
        }
        
        .nav-label {
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #2C5530;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e8f5e8;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #DAA520;
            box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.1);
        }
        
        /* Animations */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        .bounce {
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        /* Icons */
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
        }
        
        .icon-item {
            text-align: center;
            padding: 20px;
            background: #f8faf7;
            border-radius: 12px;
            border: 1px solid #e8f5e8;
        }
        
        .icon-symbol {
            font-size: 2rem;
            margin-bottom: 8px;
            display: block;
        }
        
        .icon-name {
            font-size: 0.8rem;
            color: #666;
            font-weight: 500;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Equoria Design System</h1>
            <p>A comprehensive style guide for the premium horse breeding game</p>
        </div>
        
        <!-- Color Palette -->
        <div class="section">
            <h2 class="section-title">Color Palette</h2>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #2C5530, #4A7C59);">🌲</div>
                    <div class="color-info">
                        <div class="color-name">Forest Green</div>
                        <div class="color-values">#2C5530<br/>rgb(44, 85, 48)</div>
                        <div class="color-usage">Primary brand color, navigation, headers</div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #8B4513, #D2691E);">🐴</div>
                    <div class="color-info">
                        <div class="color-name">Chestnut Brown</div>
                        <div class="color-values">#8B4513<br/>rgb(139, 69, 19)</div>
                        <div class="color-usage">Horse avatars, secondary elements</div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #DAA520, #FFD700);">🏆</div>
                    <div class="color-info">
                        <div class="color-name">Golden Yellow</div>
                        <div class="color-values">#DAA520<br/>rgb(218, 165, 32)</div>
                        <div class="color-usage">Accent color, achievements, progress</div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #F5F5DC, #FFFACD);">🌾</div>
                    <div class="color-info">
                        <div class="color-name">Cream White</div>
                        <div class="color-values">#F5F5DC<br/>rgb(245, 245, 220)</div>
                        <div class="color-usage">Backgrounds, cards, subtle highlights</div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #708090, #A9A9A9);">⚙️</div>
                    <div class="color-info">
                        <div class="color-name">Slate Gray</div>
                        <div class="color-values">#708090<br/>rgb(112, 128, 144)</div>
                        <div class="color-usage">Secondary text, inactive states</div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #DC143C, #FF6B6B);">⚠️</div>
                    <div class="color-info">
                        <div class="color-name">Ruby Red</div>
                        <div class="color-values">#DC143C<br/>rgb(220, 20, 60)</div>
                        <div class="color-usage">Alerts, critical actions, warnings</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Typography -->
        <div class="section">
            <h2 class="section-title">Typography</h2>
            
            <div class="type-specimen">
                <div class="font-name">Crimson Text - Display & Headers</div>
                <h1 style="font-family: 'Crimson Text', serif; font-size: 2.5rem; color: #2C5530; margin-bottom: 10px;">Thunder's Dawn</h1>
                <h2 style="font-family: 'Crimson Text', serif; font-size: 2rem; color: #2C5530; margin-bottom: 8px;">Competition Results</h2>
                <h3 style="font-family: 'Crimson Text', serif; font-size: 1.5rem; color: #2C5530;">Horse Profile</h3>
            </div>
            
            <div class="type-specimen">
                <div class="font-name">Inter - Body Text & UI</div>
                <p style="font-size: 1.1rem; margin-bottom: 12px; font-weight: 600;">Primary body text for important information</p>
                <p style="font-size: 1rem; margin-bottom: 12px;">Regular body text for descriptions and general content</p>
                <p style="font-size: 0.9rem; margin-bottom: 12px; color: #666;">Secondary text for metadata and supporting information</p>
                <p style="font-size: 0.8rem; color: #888;">Small text for fine print and captions</p>
            </div>
        </div>
        
        <!-- Buttons -->
        <div class="section">
            <h2 class="section-title">Buttons & Actions</h2>
            <div class="component-grid">
                <div class="component">
                    <div class="component-title">Primary Actions</div>
                    <button class="btn btn-primary">🏇 Start Training</button>
                    <button class="btn btn-primary">🏆 Enter Competition</button>
                    <button class="btn btn-primary">✨ Level Up</button>
                </div>
                
                <div class="component">
                    <div class="component-title">Secondary Actions</div>
                    <button class="btn btn-secondary">👁️ View Details</button>
                    <button class="btn btn-secondary">📊 View Stats</button>
                    <button class="btn btn-secondary">⚙️ Settings</button>
                </div>
                
                <div class="component">
                    <div class="component-title">Outline Buttons</div>
                    <button class="btn btn-outline">Cancel</button>
                    <button class="btn btn-outline">Learn More</button>
                    <button class="btn btn-outline">Share</button>
                </div>
            </div>
        </div>
        
        <!-- Cards -->
        <div class="section">
            <h2 class="section-title">Cards & Components</h2>
            <div class="component-grid">
                <div class="component">
                    <div class="component-title">Horse Card</div>
                    <div class="card horse-card">
                        <div class="horse-avatar">🐎</div>
                        <div class="horse-name">Thunder's Dawn</div>
                        <div class="horse-breed">Arabian • Level 12</div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-size: 0.8rem;">Speed</span>
                            <span style="font-size: 0.8rem; color: #DAA520;">85/100</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%;"></div>
                        </div>
                        <div style="margin-top: 16px;">
                            <span class="badge badge-level">Level 12</span>
                            <span class="badge badge-breed">Arabian</span>
                        </div>
                    </div>
                </div>
                
                <div class="component">
                    <div class="component-title">Status Card</div>
                    <div class="card">
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                            <div style="font-size: 1.5rem;">🏆</div>
                            <div>
                                <div style="font-weight: 600; color: #2C5530;">Spring Derby</div>
                                <div style="font-size: 0.9rem; color: #666;">2nd Place Finish</div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span class="badge badge-status">✅ Completed</span>
                            <span style="font-weight: 600; color: #DAA520;">+$500</span>
                        </div>
                    </div>
                </div>
                
                <div class="component">
                    <div class="component-title">Training Card</div>
                    <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <div>
                                <div style="font-weight: 600; color: #2C5530;">🏃 Speed Training</div>
                                <div style="font-size: 0.9rem; color: #666;">2 hours remaining</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 1.2rem; font-weight: 600; color: #DAA520;">1h 23m</div>
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="section">
            <h2 class="section-title">Navigation</h2>
            <div class="nav-demo">
                <a href="#" class="nav-item active">
                    <div class="nav-icon">🏠</div>
                    <div class="nav-label">Dashboard</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🐎</div>
                    <div class="nav-label">Stable</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🏆</div>
                    <div class="nav-label">Compete</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🌍</div>
                    <div class="nav-label">World</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <div class="nav-label">Settings</div>
                </a>
            </div>
        </div>
        
        <!-- Form Elements -->
        <div class="section">
            <h2 class="section-title">Form Elements</h2>
            <div class="component-grid">
                <div class="component">
                    <div class="component-title">Input Fields</div>
                    <div class="form-group">
                        <label class="form-label">Horse Name</label>
                        <input type="text" class="form-input" placeholder="Enter horse name" value="Thunder's Dawn">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Breed Selection</label>
                        <select class="form-input">
                            <option>Arabian</option>
                            <option>Thoroughbred</option>
                            <option>Quarter Horse</option>
                        </select>
                    </div>
                </div>
                
                <div class="component">
                    <div class="component-title">Progress Indicators</div>
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 0.9rem;">Training Progress</span>
                            <span style="font-size: 0.9rem; color: #DAA520;">75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-size: 0.9rem;">XP to Next Level</span>
                            <span style="font-size: 0.9rem; color: #DAA520;">450/500</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Icons -->
        <div class="section">
            <h2 class="section-title">Icon Library</h2>
            <div class="icon-grid">
                <div class="icon-item">
                    <span class="icon-symbol">🐎</span>
                    <div class="icon-name">Horse</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🏇</span>
                    <div class="icon-name">Training</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🏆</span>
                    <div class="icon-name">Competition</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🥕</span>
                    <div class="icon-name">Care</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">💰</span>
                    <div class="icon-name">Money</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">⭐</span>
                    <div class="icon-name">Level</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🏠</span>
                    <div class="icon-name">Stable</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">📊</span>
                    <div class="icon-name">Stats</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🎯</span>
                    <div class="icon-name">Goals</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🌟</span>
                    <div class="icon-name">Achievement</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">⚡</span>
                    <div class="icon-name">Energy</div>
                </div>
                <div class="icon-item">
                    <span class="icon-symbol">🔔</span>
                    <div class="icon-name">Notifications</div>
                </div>
            </div>
        </div>
        
        <!-- Usage Guidelines -->
        <div class="section">
            <h2 class="section-title">Usage Guidelines</h2>
            <div class="component-grid">
                <div class="component">
                    <div class="component-title">Do's</div>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                            <span style="color: #22c55e;">✅</span>
                            <span>Use horse-themed colors consistently</span>
                        </li>
                        <li style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                            <span style="color: #22c55e;">✅</span>
                            <span>Maintain 44px minimum touch targets</span>
                        </li>
                        <li style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                            <span style="color: #22c55e;">✅</span>
                            <span>Use Crimson Text for horse names</span>
                        </li>
                        <li style="margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
