
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  money: 'money',
  level: 'level',
  xp: 'xp',
  settings: 'settings',
  groomSalaryGracePeriod: 'groomSalaryGracePeriod',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BreedScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.StableScalarFieldEnum = {
  id: 'id',
  name: 'name',
  location: 'location'
};

exports.Prisma.HorseScalarFieldEnum = {
  id: 'id',
  name: 'name',
  sex: 'sex',
  dateOfBirth: 'dateOfBirth',
  breedId: 'breedId',
  ownerId: 'ownerId',
  stableId: 'stableId',
  genotype: 'genotype',
  phenotypicMarkings: 'phenotypicMarkings',
  finalDisplayColor: 'finalDisplayColor',
  shade: 'shade',
  imageUrl: 'imageUrl',
  trait: 'trait',
  temperament: 'temperament',
  personality: 'personality',
  precision: 'precision',
  strength: 'strength',
  speed: 'speed',
  agility: 'agility',
  endurance: 'endurance',
  intelligence: 'intelligence',
  stamina: 'stamina',
  balance: 'balance',
  coordination: 'coordination',
  boldness: 'boldness',
  flexibility: 'flexibility',
  obedience: 'obedience',
  focus: 'focus',
  totalEarnings: 'totalEarnings',
  sireId: 'sireId',
  damId: 'damId',
  studStatus: 'studStatus',
  studFee: 'studFee',
  lastBredDate: 'lastBredDate',
  forSale: 'forSale',
  salePrice: 'salePrice',
  healthStatus: 'healthStatus',
  lastVettedDate: 'lastVettedDate',
  bondScore: 'bondScore',
  stressLevel: 'stressLevel',
  daysGroomedInARow: 'daysGroomedInARow',
  burnoutStatus: 'burnoutStatus',
  taskLog: 'taskLog',
  consecutiveDaysFoalCare: 'consecutiveDaysFoalCare',
  lastGroomed: 'lastGroomed',
  tack: 'tack',
  age: 'age',
  userId: 'userId',
  trainingCooldown: 'trainingCooldown',
  earnings: 'earnings',
  rider: 'rider',
  disciplineScores: 'disciplineScores',
  epigeneticModifiers: 'epigeneticModifiers',
  conformationScores: 'conformationScores',
  horseXp: 'horseXp',
  availableStatPoints: 'availableStatPoints',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GroomScalarFieldEnum = {
  id: 'id',
  name: 'name',
  speciality: 'speciality',
  experience: 'experience',
  skillLevel: 'skillLevel',
  personality: 'personality',
  sessionRate: 'sessionRate',
  availability: 'availability',
  bio: 'bio',
  imageUrl: 'imageUrl',
  isActive: 'isActive',
  hiredDate: 'hiredDate',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GroomAssignmentScalarFieldEnum = {
  id: 'id',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  isDefault: 'isDefault',
  priority: 'priority',
  notes: 'notes',
  foalId: 'foalId',
  groomId: 'groomId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GroomInteractionScalarFieldEnum = {
  id: 'id',
  interactionType: 'interactionType',
  duration: 'duration',
  bondingChange: 'bondingChange',
  stressChange: 'stressChange',
  quality: 'quality',
  notes: 'notes',
  cost: 'cost',
  foalId: 'foalId',
  groomId: 'groomId',
  assignmentId: 'assignmentId',
  timestamp: 'timestamp',
  createdAt: 'createdAt'
};

exports.Prisma.ShowScalarFieldEnum = {
  id: 'id',
  name: 'name',
  discipline: 'discipline',
  levelMin: 'levelMin',
  levelMax: 'levelMax',
  entryFee: 'entryFee',
  prize: 'prize',
  runDate: 'runDate',
  hostUserId: 'hostUserId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompetitionResultScalarFieldEnum = {
  id: 'id',
  score: 'score',
  placement: 'placement',
  discipline: 'discipline',
  runDate: 'runDate',
  showName: 'showName',
  prizeWon: 'prizeWon',
  statGains: 'statGains',
  horseId: 'horseId',
  showId: 'showId',
  createdAt: 'createdAt'
};

exports.Prisma.TrainingLogScalarFieldEnum = {
  id: 'id',
  discipline: 'discipline',
  trainedAt: 'trainedAt',
  horseId: 'horseId'
};

exports.Prisma.FoalDevelopmentScalarFieldEnum = {
  id: 'id',
  currentDay: 'currentDay',
  bondingLevel: 'bondingLevel',
  stressLevel: 'stressLevel',
  completedActivities: 'completedActivities',
  foalId: 'foalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FoalActivityScalarFieldEnum = {
  id: 'id',
  day: 'day',
  activityType: 'activityType',
  outcome: 'outcome',
  bondingChange: 'bondingChange',
  stressChange: 'stressChange',
  description: 'description',
  foalId: 'foalId',
  createdAt: 'createdAt'
};

exports.Prisma.FoalTrainingHistoryScalarFieldEnum = {
  id: 'id',
  day: 'day',
  activity: 'activity',
  outcome: 'outcome',
  bondChange: 'bondChange',
  stressChange: 'stressChange',
  horseId: 'horseId',
  timestamp: 'timestamp',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.XpEventScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  reason: 'reason',
  userId: 'userId',
  timestamp: 'timestamp'
};

exports.Prisma.RefreshTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  userId: 'userId',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HorseXpEventScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  reason: 'reason',
  horseId: 'horseId',
  timestamp: 'timestamp'
};

exports.Prisma.GroomSalaryPaymentScalarFieldEnum = {
  id: 'id',
  groomId: 'groomId',
  userId: 'userId',
  amount: 'amount',
  paymentDate: 'paymentDate',
  paymentType: 'paymentType',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  Breed: 'Breed',
  Stable: 'Stable',
  Horse: 'Horse',
  Groom: 'Groom',
  GroomAssignment: 'GroomAssignment',
  GroomInteraction: 'GroomInteraction',
  Show: 'Show',
  CompetitionResult: 'CompetitionResult',
  TrainingLog: 'TrainingLog',
  FoalDevelopment: 'FoalDevelopment',
  FoalActivity: 'FoalActivity',
  FoalTrainingHistory: 'FoalTrainingHistory',
  XpEvent: 'XpEvent',
  RefreshToken: 'RefreshToken',
  HorseXpEvent: 'HorseXpEvent',
  GroomSalaryPayment: 'GroomSalaryPayment'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
