<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for backend/routes</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1><a href="../../index.html">All files</a> backend/routes</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/473</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/178</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/63</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/466</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th data-col="statements" data-type="number" data-fmt="pct" class="pct">
                Statements
              </th>
              <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
              <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
              <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
              <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file low" data-value="adminRoutes.js">
                <a href="adminRoutes.js.html">adminRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="44" class="abs low">0/44</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="44" class="abs low">0/44</td>
            </tr>

            <tr>
              <td class="file low" data-value="authRoutes.js">
                <a href="authRoutes.js.html">authRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
            </tr>

            <tr>
              <td class="file low" data-value="breedRoutes.js">
                <a href="breedRoutes.js.html">breedRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
            </tr>

            <tr>
              <td class="file low" data-value="competitionRoutes.js">
                <a href="competitionRoutes.js.html">competitionRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="42" class="abs low">0/42</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="20" class="abs low">0/20</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="42" class="abs low">0/42</td>
            </tr>

            <tr>
              <td class="file low" data-value="foalRoutes.js">
                <a href="foalRoutes.js.html">foalRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="65" class="abs low">0/65</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="34" class="abs low">0/34</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="65" class="abs low">0/65</td>
            </tr>

            <tr>
              <td class="file low" data-value="groomRoutes.js">
                <a href="groomRoutes.js.html">groomRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="14" class="abs low">0/14</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="1" class="abs low">0/1</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="14" class="abs low">0/14</td>
            </tr>

            <tr>
              <td class="file low" data-value="horseRoutes.js">
                <a href="horseRoutes.js.html">horseRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="36" class="abs low">0/36</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="14" class="abs low">0/14</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="36" class="abs low">0/36</td>
            </tr>

            <tr>
              <td class="file low" data-value="leaderboardRoutes.js">
                <a href="leaderboardRoutes.js.html">leaderboardRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="8" class="abs low">0/8</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="8" class="abs low">0/8</td>
            </tr>

            <tr>
              <td class="file low" data-value="ping.js"><a href="ping.js.html">ping.js</a></td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
            </tr>

            <tr>
              <td class="file low" data-value="trainingRoutes.js">
                <a href="trainingRoutes.js.html">trainingRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="45" class="abs low">0/45</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="10" class="abs low">0/10</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="7" class="abs low">0/7</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="45" class="abs low">0/45</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitDiscoveryRoutes.js">
                <a href="traitDiscoveryRoutes.js.html">traitDiscoveryRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="86" class="abs low">0/86</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="36" class="abs low">0/36</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="19" class="abs low">0/19</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="83" class="abs low">0/83</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitRoutes.js">
                <a href="traitRoutes.js.html">traitRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="19" class="abs low">0/19</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="6" class="abs low">0/6</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="3" class="abs low">0/3</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="18" class="abs low">0/18</td>
            </tr>

            <tr>
              <td class="file low" data-value="userRoutes.js">
                <a href="userRoutes.js.html">userRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="15" class="abs low">0/15</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="2" class="abs low">0/2</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="15" class="abs low">0/15</td>
            </tr>

            <tr>
              <td class="file low" data-value="xpRoutes.js">
                <a href="xpRoutes.js.html">xpRoutes.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="86" class="abs low">0/86</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="52" class="abs low">0/52</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="11" class="abs low">0/11</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="83" class="abs low">0/83</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="../../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../../sorter.js"></script>
    <script src="../../block-navigation.js"></script>
  </body>
</html>
