{"watch": ["*.mjs", "*.js", "controllers/**/*.mjs", "routes/**/*.mjs", "middleware/**/*.mjs", "models/**/*.mjs", "utils/**/*.mjs", "config/**/*.mjs", "services/**/*.mjs"], "ext": "mjs,js,json", "ignore": ["node_modules/**/*", "tests/**/*", "coverage/**/*", "*.test.mjs", "*.test.js", "dist/**/*", "build/**/*"], "exec": "node server.mjs", "env": {"NODE_ENV": "development"}, "delay": 1000, "verbose": true, "restartable": "rs", "colours": true, "legacyWatch": false}