{"info": {"name": "Groom API Tests", "description": "Comprehensive testing of groom system API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "userId", "value": "83970fb4-f086-46b3-9e76-ae71720d2918", "type": "string"}, {"key": "foalId", "value": "1", "type": "string"}, {"key": "youngHorseId", "value": "2", "type": "string"}, {"key": "adultHorseId", "value": "3", "type": "string"}, {"key": "groomId", "value": "1", "type": "string"}], "item": [{"name": "0. Cleanup Test Data", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/grooms/test/cleanup", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "test", "cleanup"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Cleanup completed', function () {", "    // Allow any status - cleanup might fail if no data exists", "    console.log('Test data cleanup completed');", "});"]}}]}, {"name": "1. Get Groom Definitions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grooms/definitions", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "definitions"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has definitions', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('specialties');", "    pm.expect(jsonData.data).to.have.property('skillLevels');", "    pm.expect(jsonData.data).to.have.property('personalities');", "});"]}}]}, {"name": "2. <PERSON><PERSON> Foal Care Groom", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"speciality\": \"foal_care\",\n  \"skill_level\": \"expert\",\n  \"personality\": \"gentle\",\n  \"experience\": 10,\n  \"session_rate\": 30.0,\n  \"bio\": \"Expert foal care specialist for testing\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/hire", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "hire"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Groom created with correct data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.name).to.eql('<PERSON>');", "    pm.expect(jsonData.data.speciality).to.eql('foal_care');", "    pm.expect(jsonData.data.skillLevel).to.eql('expert');", "    pm.globals.set('newGroomId', jsonData.data.id);", "});"]}}]}, {"name": "3. Get User's Grooms", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grooms/user/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "user", "{{userId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Returns array of grooms', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.grooms).to.be.an('array');", "    pm.expect(jsonData.data.grooms.length).to.be.at.least(2);", "});"]}}]}, {"name": "4. <PERSON><PERSON> to Foal", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"foalId\": {{foalId}},\n  \"groomId\": {{groomId}},\n  \"priority\": 1,\n  \"notes\": \"Primary caregiver for daily enrichment tasks\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/assign", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "assign"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Assignment created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.foalId).to.eql(1);", "    pm.expect(jsonData.data.groomId).to.eql(1);", "});"]}}]}, {"name": "5. <PERSON>oal's Assignments", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grooms/assignments/{{foalId}}", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "assignments", "{{foalId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Returns foal assignments', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('assignments');", "    pm.expect(jsonData.data.assignments).to.be.an('array');", "});"]}}]}, {"name": "6. Record Foal Enrichment Task (Valid)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"foalId\": {{foalId}},\n  \"groomId\": {{groomId}},\n  \"interactionType\": \"trust_building\",\n  \"duration\": 30,\n  \"notes\": \"Foal responded well to gentle handling and trust exercises\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/interact", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "interact"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Interaction recorded successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "    pm.expect(jsonData.data).to.have.property('interaction');", "    pm.expect(jsonData.data).to.have.property('effects');", "});"]}}]}, {"name": "7. Record Second Task Same Day (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"foalId\": {{foalId}},\n  \"groomId\": {{groomId}},\n  \"interactionType\": \"desensitization\",\n  \"duration\": 20,\n  \"notes\": \"Attempting second task same day\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/interact", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "interact"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about task exclusivity', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', false);", "    pm.expect(jsonData.message).to.include('already had a groom interaction today');", "});"]}}]}, {"name": "7.5. Cleanup for Age Test", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/grooms/test/cleanup", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "test", "cleanup"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Cleanup for age test completed', function () {", "    // Allow any status - cleanup might fail if no data exists", "    console.log('Cleanup completed before age restriction test');", "});"]}}]}, {"name": "8. Test Age Gating - Adult Task on Foal (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"foalId\": {{foalId}},\n  \"groomId\": {{groomId}},\n  \"interactionType\": \"brushing\",\n  \"duration\": 30,\n  \"notes\": \"Attempting adult task on foal\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/interact", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "interact"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about age restriction', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', false);", "    pm.expect(jsonData.message).to.include('brushing');", "    pm.expect(jsonData.message).to.include('not an eligible task');", "});"]}}]}, {"name": "9. <PERSON> - Foal Grooming Task (Valid)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"foalId\": {{youngHorseId}},\n  \"groomId\": {{groomId}},\n  \"interactionType\": \"early_touch\",\n  \"duration\": 25,\n  \"notes\": \"Young horse responding well to early grooming\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/interact", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "interact"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Young horse grooming task successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}, {"name": "10. Test Adult Horse - General <PERSON> (Valid)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"foalId\": {{adultHorseId}},\n  \"groomId\": {{groomId}},\n  \"interactionType\": \"hand-walking\",\n  \"duration\": 45,\n  \"notes\": \"Adult horse enjoying hand walking exercise\"\n}"}, "url": {"raw": "{{baseUrl}}/api/grooms/interact", "host": ["{{baseUrl}}"], "path": ["api", "grooms", "interact"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Adult horse grooming task successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}]}