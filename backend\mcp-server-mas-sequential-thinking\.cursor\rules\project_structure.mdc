---
description: 
globs: 
alwaysApply: true
---
# Project Structure and Key Components

This is a Python project implementing a Multi-Agent System (MAS) for sequential thinking using the **Agno** framework and served via **MCP**.

## Core Files & Logic

*   **Main Entry Point & Server Logic:** [`main.py`](mdc:main.py) - Sets up the FastMCP server, defines the `sequentialthinking` tool, instantiates the agents, and contains the primary coordination logic using the `Team` object from Agno.
*   **Dependencies:** [`pyproject.toml`](mdc:pyproject.toml) and [`uv.lock`](mdc:uv.lock) define and lock project dependencies, managed preferably with `uv`.
*   **Configuration:** Relies on environment variables (often stored in a `.env` file, see [`.gitignore`](mdc:.gitignore)) for API keys (Groq, DeepSeek, OpenRouter, Exa) and LLM model selection (`LLM_PROVIDER`, `*_MODEL_ID`, etc.).

## Key Concepts & Libraries

*   **Multi-Agent System (MAS):** Built using the **Agno** framework.
    *   **Coordinator:** A `Team` object manages the workflow.
    *   **Specialist Agents:** Roles like <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lyzer, Critic, Synthesizer handle sub-tasks (likely defined within [`main.py`](mdc:main.py) or imported modules).
*   **Sequential Thinking Tool:** The primary functionality exposed is the `sequentialthinking` tool, which takes `ThoughtData` (likely defined via Pydantic models in [`main.py`](mdc:main.py) or related files) as input.
*   **Data Validation:** Uses **Pydantic** for robust input and data structure validation.
*   **External Tools:** Can integrate with tools like Exa via the Researcher agent.

## Documentation

*   **Primary README:** [`README.md`](mdc:README.md)
*   **Chinese README:** [`README.zh-CN.md`](mdc:README.zh-CN.md)

Understanding the interaction between the Coordinator (`Team` in `coordinate` mode) and the specialist agents within [`main.py`](mdc:main.py) is crucial for modifying the core sequential thinking logic. Refer to the [`README.md`](mdc:README.md) for detailed workflow explanations.