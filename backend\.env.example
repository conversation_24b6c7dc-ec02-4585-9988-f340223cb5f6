# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL="postgres://username:password@localhost:5432/equoria"

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# JWT Authentication
# IMPORTANT: Generate a strong, random secret for production!
# You can use: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-please-make-it-very-long-and-random

# Optional: Logging Configuration
LOG_LEVEL=info

# Optional: Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Optional: Security Configuration
BCRYPT_SALT_ROUNDS=12
JWT_ACCESS_TOKEN_EXPIRY=24h
JWT_REFRESH_TOKEN_EXPIRY=7d
