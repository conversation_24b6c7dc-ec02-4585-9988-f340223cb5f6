import * as __banner_node_module from "node:module";
import * as __banner_node_path from "node:path";
import * as process from "node:process";
import * as __banner_node_url from "node:url";
const __filename = __banner_node_url.fileURLToPath(import.meta.url);
const __dirname = __banner_node_path.dirname(__filename);
const require = __banner_node_module.createRequire(import.meta.url);
var Gu=Object.create;var Gn=Object.defineProperty;var Wu=Object.getOwnPropertyDescriptor;var Ju=Object.getOwnPropertyNames;var Ku=Object.getPrototypeOf,zu=Object.prototype.hasOwnProperty;var Mt=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var Ro=(e,t)=>()=>(e&&(t=e(e=0)),t);var B=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ft=(e,t)=>{for(var r in t)Gn(e,r,{get:t[r],enumerable:!0})},Yu=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ju(t))!zu.call(e,i)&&i!==r&&Gn(e,i,{get:()=>t[i],enumerable:!(n=Wu(t,i))||n.enumerable});return e};var ge=(e,t,r)=>(r=e!=null?Gu(Ku(e)):{},Yu(t||!e||!e.__esModule?Gn(r,"default",{value:e,enumerable:!0}):r,e));var Qo=B((Bg,Ec)=>{Ec.exports={name:"@prisma/internals",version:"6.8.2",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/schema-engine-wasm":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var Go=B((Gg,Ho)=>{"use strict";Ho.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var zo=B((Kg,Ko)=>{"use strict";Ko.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Xo=B((Yg,Zo)=>{"use strict";Zo.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var Xn=B((Zg,es)=>{"use strict";var Sc=Xo();es.exports=e=>typeof e=="string"?e.replace(Sc(),""):e});var ts=B((rh,Rc)=>{Rc.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var ss=B((nh,Ce)=>{"use strict";var ti=Mt("node:fs"),ri=Mt("node:path"),kc=Mt("node:os"),Ic=Mt("node:crypto"),Oc=ts(),ns=Oc.version,Dc=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function _c(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=Dc.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function Nc(e){let t=os(e),r=q.configDotenv({path:t});if(!r.parsed){let s=new Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw s.code="MISSING_DATA",s}let n=is(e).split(","),i=n.length,o;for(let s=0;s<i;s++)try{let a=n[s].trim(),l=Fc(r,a);o=q.decrypt(l.ciphertext,l.key);break}catch(a){if(s+1>=i)throw a}return q.parse(o)}function Mc(e){console.log(`[dotenv@${ns}][WARN] ${e}`)}function Bt(e){console.log(`[dotenv@${ns}][DEBUG] ${e}`)}function is(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function Fc(e,t){let r;try{r=new URL(t)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let n=r.password;if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let i=r.searchParams.get("environment");if(!i){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:s,key:n}}function os(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)ti.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=ri.resolve(process.cwd(),".env.vault");return ti.existsSync(t)?t:null}function rs(e){return e[0]==="~"?ri.join(kc.homedir(),e.slice(1)):e}function Lc(e){!!(e&&e.debug)&&Bt("Loading env from encrypted .env.vault");let r=q._parseVault(e),n=process.env;return e&&e.processEnv!=null&&(n=e.processEnv),q.populate(n,r,e),{parsed:r}}function $c(e){let t=ri.resolve(process.cwd(),".env"),r="utf8",n=!!(e&&e.debug);e&&e.encoding?r=e.encoding:n&&Bt("No encoding is specified. UTF-8 is used by default");let i=[t];if(e&&e.path)if(!Array.isArray(e.path))i=[rs(e.path)];else{i=[];for(let l of e.path)i.push(rs(l))}let o,s={};for(let l of i)try{let u=q.parse(ti.readFileSync(l,{encoding:r}));q.populate(s,u,e)}catch(u){n&&Bt(`Failed to load ${l} ${u.message}`),o=u}let a=process.env;return e&&e.processEnv!=null&&(a=e.processEnv),q.populate(a,s,e),o?{parsed:s,error:o}:{parsed:s}}function Vc(e){if(is(e).length===0)return q.configDotenv(e);let t=os(e);return t?q._configVault(e):(Mc(`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`),q.configDotenv(e))}function qc(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let s=Ic.createDecipheriv("aes-256-gcm",r,i);return s.setAuthTag(o),`${s.update(n)}${s.final()}`}catch(s){let a=s instanceof RangeError,l=s.message==="Invalid key length",u=s.message==="Unsupported state or unable to authenticate data";if(a||l){let c=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw c.code="INVALID_DOTENV_KEY",c}else if(u){let c=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw c.code="DECRYPTION_FAILED",c}else throw s}}function jc(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if(typeof t!="object"){let o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(let o of Object.keys(t))Object.prototype.hasOwnProperty.call(e,o)?(i===!0&&(e[o]=t[o]),n&&Bt(i===!0?`"${o}" is already defined and WAS overwritten`:`"${o}" is already defined and was NOT overwritten`)):e[o]=t[o]}var q={configDotenv:$c,_configVault:Lc,_parseVault:Nc,config:Vc,decrypt:qc,parse:_c,populate:jc};Ce.exports.configDotenv=q.configDotenv;Ce.exports._configVault=q._configVault;Ce.exports._parseVault=q._parseVault;Ce.exports.config=q.config;Ce.exports.decrypt=q.decrypt;Ce.exports.parse=q.parse;Ce.exports.populate=q.populate;Ce.exports=q});var cs=B((ph,jr)=>{"use strict";jr.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};jr.exports.default=jr.exports});var fi=B((jy,Ds)=>{"use strict";Ds.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,u,c,p,d,m,g,h,R,T,C,w,k=[];for(l=0;l<i;l++)k.push(l+1),k.push(t.charCodeAt(s+l));for(var ce=k.length-1;a<o-3;)for(R=r.charCodeAt(s+(u=a)),T=r.charCodeAt(s+(c=a+1)),C=r.charCodeAt(s+(p=a+2)),w=r.charCodeAt(s+(d=a+3)),m=a+=4,l=0;l<ce;l+=2)g=k[l],h=k[l+1],u=e(g,u,c,R,h),c=e(u,c,p,T,h),p=e(c,p,d,C,h),m=e(p,d,m,w,h),k[l]=m,d=p,p=c,c=u,u=g;for(;a<o;)for(R=r.charCodeAt(s+(u=a)),m=++a,l=0;l<ce;l+=2)g=k[l],k[l]=m=e(g,u,m,R,k[l+1]),u=g;return m}}()});var Ls=Ro(()=>{"use strict"});var $s=Ro(()=>{"use strict"});var oa=B((hb,qd)=>{qd.exports={name:"@prisma/engines-version",version:"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var qi=B(Je=>{"use strict";Object.defineProperty(Je,"__esModule",{value:!0});Je.anumber=Vi;Je.abytes=Ya;Je.ahash=Um;Je.aexists=Bm;Je.aoutput=Qm;function Vi(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function jm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Ya(e,...t){if(!jm(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Um(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Vi(e.outputLen),Vi(e.blockLen)}function Bm(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function Qm(e,t){Ya(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var El=B(P=>{"use strict";Object.defineProperty(P,"__esModule",{value:!0});P.add5L=P.add5H=P.add4H=P.add4L=P.add3H=P.add3L=P.rotlBL=P.rotlBH=P.rotlSL=P.rotlSH=P.rotr32L=P.rotr32H=P.rotrBL=P.rotrBH=P.rotrSL=P.rotrSH=P.shrSL=P.shrSH=P.toBig=void 0;P.fromBig=Ui;P.split=Za;P.add=dl;var Pn=BigInt(2**32-1),ji=BigInt(32);function Ui(e,t=!1){return t?{h:Number(e&Pn),l:Number(e>>ji&Pn)}:{h:Number(e>>ji&Pn)|0,l:Number(e&Pn)|0}}function Za(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=Ui(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var Xa=(e,t)=>BigInt(e>>>0)<<ji|BigInt(t>>>0);P.toBig=Xa;var el=(e,t,r)=>e>>>r;P.shrSH=el;var tl=(e,t,r)=>e<<32-r|t>>>r;P.shrSL=tl;var rl=(e,t,r)=>e>>>r|t<<32-r;P.rotrSH=rl;var nl=(e,t,r)=>e<<32-r|t>>>r;P.rotrSL=nl;var il=(e,t,r)=>e<<64-r|t>>>r-32;P.rotrBH=il;var ol=(e,t,r)=>e>>>r-32|t<<64-r;P.rotrBL=ol;var sl=(e,t)=>t;P.rotr32H=sl;var al=(e,t)=>e;P.rotr32L=al;var ll=(e,t,r)=>e<<r|t>>>32-r;P.rotlSH=ll;var ul=(e,t,r)=>t<<r|e>>>32-r;P.rotlSL=ul;var cl=(e,t,r)=>t<<r-32|e>>>64-r;P.rotlBH=cl;var pl=(e,t,r)=>e<<r-32|t>>>64-r;P.rotlBL=pl;function dl(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var ml=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);P.add3L=ml;var fl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;P.add3H=fl;var gl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);P.add4L=gl;var hl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;P.add4H=hl;var yl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);P.add5L=yl;var wl=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;P.add5H=wl;var Hm={fromBig:Ui,split:Za,toBig:Xa,shrSH:el,shrSL:tl,rotrSH:rl,rotrSL:nl,rotrBH:il,rotrBL:ol,rotr32H:sl,rotr32L:al,rotlSH:ll,rotlSL:ul,rotlBH:cl,rotlBL:pl,add:dl,add3L:ml,add3H:fl,add4L:gl,add4H:hl,add5H:wl,add5L:yl};P.default=Hm});var bl=B(vn=>{"use strict";Object.defineProperty(vn,"__esModule",{value:!0});vn.crypto=void 0;var $e=Mt("node:crypto");vn.crypto=$e&&typeof $e=="object"&&"webcrypto"in $e?$e.webcrypto:$e&&typeof $e=="object"&&"randomBytes"in $e?$e:void 0});var vl=B(S=>{"use strict";Object.defineProperty(S,"__esModule",{value:!0});S.Hash=S.nextTick=S.byteSwapIfBE=S.isLE=void 0;S.isBytes=Gm;S.u8=Wm;S.u32=Jm;S.createView=Km;S.rotr=zm;S.rotl=Ym;S.byteSwap=Hi;S.byteSwap32=Zm;S.bytesToHex=ef;S.hexToBytes=tf;S.asyncLoop=nf;S.utf8ToBytes=Pl;S.toBytes=Tn;S.concatBytes=of;S.checkOpts=sf;S.wrapConstructor=af;S.wrapConstructorWithOpts=lf;S.wrapXOFConstructorWithOpts=uf;S.randomBytes=cf;var xt=bl(),Qi=qi();function Gm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Wm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Jm(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Km(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function zm(e,t){return e<<32-t|e>>>t}function Ym(e,t){return e<<t|e>>>32-t>>>0}S.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Hi(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}S.byteSwapIfBE=S.isLE?e=>e:e=>Hi(e);function Zm(e){for(let t=0;t<e.length;t++)e[t]=Hi(e[t])}var Xm=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function ef(e){(0,Qi.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=Xm[e[r]];return t}var Re={_0:48,_9:57,A:65,F:70,a:97,f:102};function xl(e){if(e>=Re._0&&e<=Re._9)return e-Re._0;if(e>=Re.A&&e<=Re.F)return e-(Re.A-10);if(e>=Re.a&&e<=Re.f)return e-(Re.a-10)}function tf(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=xl(e.charCodeAt(o)),a=xl(e.charCodeAt(o+1));if(s===void 0||a===void 0){let l=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+o)}n[i]=s*16+a}return n}var rf=async()=>{};S.nextTick=rf;async function nf(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,S.nextTick)(),n+=o)}}function Pl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Tn(e){return typeof e=="string"&&(e=Pl(e)),(0,Qi.abytes)(e),e}function of(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,Qi.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var Bi=class{clone(){return this._cloneInto()}};S.Hash=Bi;function sf(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function af(e){let t=n=>e().update(Tn(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function lf(e){let t=(n,i)=>e(i).update(Tn(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function uf(e){let t=(n,i)=>e(i).update(Tn(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function cf(e=32){if(xt.crypto&&typeof xt.crypto.getRandomValues=="function")return xt.crypto.getRandomValues(new Uint8Array(e));if(xt.crypto&&typeof xt.crypto.randomBytes=="function")return xt.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var Ol=B(F=>{"use strict";Object.defineProperty(F,"__esModule",{value:!0});F.shake256=F.shake128=F.keccak_512=F.keccak_384=F.keccak_256=F.keccak_224=F.sha3_512=F.sha3_384=F.sha3_256=F.sha3_224=F.Keccak=void 0;F.keccakP=kl;var Pt=qi(),fr=El(),ke=vl(),Al=[],Sl=[],Rl=[],pf=BigInt(0),mr=BigInt(1),df=BigInt(2),mf=BigInt(7),ff=BigInt(256),gf=BigInt(113);for(let e=0,t=mr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],Al.push(2*(5*n+r)),Sl.push((e+1)*(e+2)/2%64);let i=pf;for(let o=0;o<7;o++)t=(t<<mr^(t>>mf)*gf)%ff,t&df&&(i^=mr<<(mr<<BigInt(o))-mr);Rl.push(i)}var[hf,yf]=(0,fr.split)(Rl,!0),Tl=(e,t,r)=>r>32?(0,fr.rotlBH)(e,t,r):(0,fr.rotlSH)(e,t,r),Cl=(e,t,r)=>r>32?(0,fr.rotlBL)(e,t,r):(0,fr.rotlSL)(e,t,r);function kl(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,l=(s+2)%10,u=r[l],c=r[l+1],p=Tl(u,c,1)^r[a],d=Cl(u,c,1)^r[a+1];for(let m=0;m<50;m+=10)e[s+m]^=p,e[s+m+1]^=d}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=Sl[s],l=Tl(i,o,a),u=Cl(i,o,a),c=Al[s];i=e[c],o=e[c+1],e[c]=l,e[c+1]=u}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=hf[n],e[1]^=yf[n]}r.fill(0)}var gr=class e extends ke.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Pt.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,ke.u32)(this.state)}keccak(){ke.isLE||(0,ke.byteSwap32)(this.state32),kl(this.state32,this.rounds),ke.isLE||(0,ke.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Pt.aexists)(this);let{blockLen:r,state:n}=this;t=(0,ke.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,Pt.aexists)(this,!1),(0,Pt.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Pt.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Pt.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};F.Keccak=gr;var Ve=(e,t,r)=>(0,ke.wrapConstructor)(()=>new gr(t,e,r));F.sha3_224=Ve(6,144,224/8);F.sha3_256=Ve(6,136,256/8);F.sha3_384=Ve(6,104,384/8);F.sha3_512=Ve(6,72,512/8);F.keccak_224=Ve(1,144,224/8);F.keccak_256=Ve(1,136,256/8);F.keccak_384=Ve(1,104,384/8);F.keccak_512=Ve(1,72,512/8);var Il=(e,t,r)=>(0,ke.wrapXOFConstructorWithOpts)((n={})=>new gr(t,e,n.dkLen===void 0?r:n.dkLen,!0));F.shake128=Il(31,168,128/8);F.shake256=Il(31,136,256/8)});var Vl=B((Q0,qe)=>{"use strict";var{sha3_512:wf}=Ol(),_l=24,hr=32,Gi=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function Nl(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var Ml=(e="")=>Nl(wf(e)).toString(36).slice(1),Dl=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),Ef=e=>Dl[Math.floor(e()*Dl.length)],Fl=({globalObj:e=typeof global<"u"?global:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+Gi(hr,t):Gi(hr,t);return Ml(n).substring(0,hr)},Ll=e=>()=>e++,bf=476782367,$l=({random:e=Math.random,counter:t=Ll(Math.floor(e()*bf)),length:r=_l,fingerprint:n=Fl({random:e})}={})=>function(){let o=Ef(e),s=Date.now().toString(36),a=t().toString(36),l=Gi(r,e),u=`${s+l+a+n}`;return`${o+Ml(u).substring(1,r)}`},xf=$l(),Pf=(e,{minLength:t=2,maxLength:r=hr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};qe.exports.getConstants=()=>({defaultLength:_l,bigLength:hr});qe.exports.init=$l;qe.exports.createId=xf;qe.exports.bufToBigInt=Nl;qe.exports.createCounter=Ll;qe.exports.createFingerprint=Fl;qe.exports.isCuid=Pf});var ql=B((H0,yr)=>{"use strict";var{createId:vf,init:Tf,getConstants:Cf,isCuid:Af}=Vl();yr.exports.createId=vf;yr.exports.init=Tf;yr.exports.getConstants=Cf;yr.exports.isCuid=Af});var Oo={};Ft(Oo,{defineExtension:()=>ko,getExtensionContext:()=>Io});function ko(e){return typeof e=="function"?e:t=>t.$extends(e)}function Io(e){return e}var _o={};Ft(_o,{validator:()=>Do});function Do(...e){return t=>t}var Lr={};Ft(Lr,{$:()=>$o,bgBlack:()=>ac,bgBlue:()=>pc,bgCyan:()=>mc,bgGreen:()=>uc,bgMagenta:()=>dc,bgRed:()=>lc,bgWhite:()=>fc,bgYellow:()=>cc,black:()=>nc,blue:()=>Qe,bold:()=>z,cyan:()=>Te,dim:()=>Ue,gray:()=>$t,green:()=>Lt,grey:()=>sc,hidden:()=>tc,inverse:()=>ec,italic:()=>Xu,magenta:()=>ic,red:()=>ve,reset:()=>Zu,strikethrough:()=>rc,underline:()=>re,white:()=>oc,yellow:()=>Be});var Wn,No,Mo,Fo,Lo=!0;typeof process<"u"&&({FORCE_COLOR:Wn,NODE_DISABLE_COLORS:No,NO_COLOR:Mo,TERM:Fo}=process.env||{},Lo=process.stdout&&process.stdout.isTTY);var $o={enabled:!No&&Mo==null&&Fo!=="dumb"&&(Wn!=null&&Wn!=="0"||Lo)};function N(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!$o.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var Zu=N(0,0),z=N(1,22),Ue=N(2,22),Xu=N(3,23),re=N(4,24),ec=N(7,27),tc=N(8,28),rc=N(9,29),nc=N(30,39),ve=N(31,39),Lt=N(32,39),Be=N(33,39),Qe=N(34,39),ic=N(35,39),Te=N(36,39),oc=N(37,39),$t=N(90,39),sc=N(90,39),ac=N(40,49),lc=N(41,49),uc=N(42,49),cc=N(43,49),pc=N(44,49),dc=N(45,49),mc=N(46,49),fc=N(47,49);var gc=100,Vo=["green","yellow","blue","magenta","cyan","red"],Vt=[],qo=Date.now(),hc=0,Jn=typeof process<"u"?process.env:{};globalThis.DEBUG??=Jn.DEBUG??"";globalThis.DEBUG_COLORS??=Jn.DEBUG_COLORS?Jn.DEBUG_COLORS==="true":!0;var qt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function yc(e){let t={color:Vo[hc++%Vo.length],enabled:qt.enabled(e),namespace:e,log:qt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Vt.push([o,...n]),Vt.length>gc&&Vt.shift(),qt.enabled(o)||i){let l=n.map(c=>typeof c=="string"?c:wc(c)),u=`+${Date.now()-qo}ms`;qo=Date.now(),globalThis.DEBUG_COLORS?a(Lr[s](z(o)),...l,Lr[s](u)):a(o,...l,u)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var L=new Proxy(yc,{get:(e,t)=>qt[t],set:(e,t,r)=>qt[t]=r});function wc(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function jo(e=7500){let t=Vt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function Uo(){Vt.length=0}var Bo=L;var bc=Qo(),Kn=bc.version;function it(e){let t=xc();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":Pc(e))}function xc(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function Pc(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}var Wo=ge(Go(),1);function zn(e){let t=(0,Wo.default)(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}var Jo="prisma+postgres",$r=`${Jo}:`;function Vr(e){return e?.toString().startsWith(`${$r}//`)??!1}function Yn(e){if(!Vr(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}var Ut={};Ft(Ut,{error:()=>Cc,info:()=>Tc,log:()=>vc,query:()=>Ac,should:()=>Yo,tags:()=>jt,warn:()=>Zn});var jt={error:ve("prisma:error"),warn:Be("prisma:warn"),info:Te("prisma:info"),query:Qe("prisma:query")},Yo={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function vc(...e){console.log(...e)}function Zn(e,...t){Yo.warn()&&console.warn(`${jt.warn} ${e}`,...t)}function Tc(e,...t){console.info(`${jt.info} ${e}`,...t)}function Cc(e,...t){console.error(`${jt.error} ${e}`,...t)}function Ac(e,...t){console.log(`${jt.query} ${e}`,...t)}function pe(e,t){throw new Error(t)}import qr from"node:path";function ei(e){return qr.sep===qr.posix.sep?e:e.split(qr.sep).join(qr.posix.sep)}var oi=ge(ss());import ni from"node:fs";import Qt from"node:path";function as(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],u,c;if(l==="\\")c=a[0],u=c.replace("\\$","$");else{let p=a[2];c=a[0].substring(l.length),u=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",u=r(u)}return o.replace(c,u)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var ii=Bo("prisma:tryLoadEnv");function Ht({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=ls(e);r.conflictCheck!=="none"&&Uc(n,t,r.conflictCheck);let i=null;return us(n?.path,t)||(i=ls(t)),!n&&!i&&ii("No Environment variables loaded"),i?.dotenvResult.error?console.error(ve(z("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function Uc(e,t,r){let n=e?.dotenvResult.parsed,i=!us(e?.path,t);if(n&&t&&i&&ni.existsSync(t)){let o=oi.default.parse(ni.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=Qt.relative(process.cwd(),e.path),l=Qt.relative(process.cwd(),t);if(r==="error"){let u=`There is a conflict between env var${s.length>1?"s":""} in ${re(a)} and ${re(l)}
Conflicting env vars:
${s.map(c=>`  ${z(c)}`).join(`
`)}

We suggest to move the contents of ${re(l)} to ${re(a)} to consolidate your env vars.
`;throw new Error(u)}else if(r==="warn"){let u=`Conflict for env var${s.length>1?"s":""} ${s.map(c=>z(c)).join(", ")} in ${re(a)} and ${re(l)}
Env vars from ${re(l)} overwrite the ones from ${re(a)}
      `;console.warn(`${Be("warn(prisma)")} ${u}`)}}}}function ls(e){if(Bc(e)){ii(`Environment variables loaded from ${e}`);let t=oi.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:as(t),message:Ue(`Environment variables loaded from ${Qt.relative(process.cwd(),e)}`),path:e}}else ii(`Environment variables not found at ${e}`);return null}function us(e,t){return e&&t&&Qt.resolve(e)===Qt.resolve(t)}function Bc(e){return!!(e&&ni.existsSync(e))}function si(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ot(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function ai(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function x(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var ps=new Set,Ur=(e,t,...r)=>{ps.has(e)||(ps.add(e),Zn(t,...r))};var I=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};x(I,"PrismaClientInitializationError");var Q=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};x(Q,"PrismaClientKnownRequestError");var ne=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};x(ne,"PrismaClientRustPanicError");var Y=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};x(Y,"PrismaClientUnknownRequestError");var Z=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};x(Z,"PrismaClientValidationError");var st=9e15,Ne=1e9,li="0123456789abcdef",Hr="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",Gr="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",ui={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-st,maxE:st,crypto:!1},hs,Ae,b=!0,Jr="[DecimalError] ",_e=Jr+"Invalid argument: ",ys=Jr+"Precision limit exceeded",ws=Jr+"crypto unavailable",Es="[object Decimal]",K=Math.floor,j=Math.pow,Qc=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,Hc=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,Gc=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,bs=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,de=1e7,E=7,Wc=9007199254740991,Jc=Hr.length-1,ci=Gr.length-1,f={toStringTag:Es};f.absoluteValue=f.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),y(e)};f.ceil=function(){return y(new this.constructor(this),this.e+1,2)};f.clampedTo=f.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(_e+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};f.comparedTo=f.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,u=e.s;if(!s||!a)return!l||!u?NaN:l!==u?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-u:0;if(l!==u)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};f.cosine=f.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=Kc(n,Cs(n,r)),n.precision=e,n.rounding=t,y(Ae==2||Ae==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};f.cubeRoot=f.cbrt=function(){var e,t,r,n,i,o,s,a,l,u,c=this,p=c.constructor;if(!c.isFinite()||c.isZero())return new p(c);for(b=!1,o=c.s*j(c.s*c,1/3),!o||Math.abs(o)==1/0?(r=H(c.d),e=c.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=j(r,1/3),e=K((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=c.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),u=l.plus(c),n=_(u.plus(c).times(a),u.plus(l),s+2,1),H(a.d).slice(0,s)===(r=H(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(y(a,e+1,0),a.times(a).times(a).eq(c))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(y(n,e+1,1),t=!n.times(n).times(n).eq(c));break}return b=!0,y(n,e,p.rounding,t)};f.decimalPlaces=f.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-K(this.e/E))*E,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};f.dividedBy=f.div=function(e){return _(this,new this.constructor(e))};f.dividedToIntegerBy=f.divToInt=function(e){var t=this,r=t.constructor;return y(_(t,new r(e),0,1,1),r.precision,r.rounding)};f.equals=f.eq=function(e){return this.cmp(e)===0};f.floor=function(){return y(new this.constructor(this),this.e+1,3)};f.greaterThan=f.gt=function(e){return this.cmp(e)>0};f.greaterThanOrEqualTo=f.gte=function(e){var t=this.cmp(e);return t==1||t===0};f.hyperbolicCosine=f.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/zr(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=at(s,1,o.times(t),new s(1),!0);for(var l,u=e,c=new s(8);u--;)l=o.times(o),o=a.minus(l.times(c.minus(l.times(c))));return y(o,s.precision=r,s.rounding=n,!0)};f.hyperbolicSine=f.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=at(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/zr(5,e)),i=at(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),u=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(u))))}return o.precision=t,o.rounding=r,y(i,t,r,!0)};f.hyperbolicTangent=f.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,_(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};f.inverseCosine=f.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return r!==-1?r===0?e.isNeg()?he(t,n,i):new t(0):new t(NaN):e.isZero()?he(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))};f.inverseHyperbolicCosine=f.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,b=!1,r=r.times(r).minus(1).sqrt().plus(r),b=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};f.inverseHyperbolicSine=f.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,b=!1,r=r.times(r).plus(1).sqrt().plus(r),b=!0,n.precision=e,n.rounding=t,r.ln())};f.inverseHyperbolicTangent=f.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?y(new o(i),e,t,!0):(o.precision=r=n-i.e,i=_(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};f.inverseSine=f.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=he(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};f.inverseTangent=f.atan=function(){var e,t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&p+4<=ci)return s=he(c,p+4,d).times(.25),s.s=u.s,s}else{if(!u.s)return new c(NaN);if(p+4<=ci)return s=he(c,p+4,d).times(.5),s.s=u.s,s}for(c.precision=a=p+10,c.rounding=1,r=Math.min(28,a/E+2|0),e=r;e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(b=!1,t=Math.ceil(a/E),n=1,l=u.times(u),s=new c(u),i=u;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),b=!0,y(s,c.precision=p,c.rounding=d,!0)};f.isFinite=function(){return!!this.d};f.isInteger=f.isInt=function(){return!!this.d&&K(this.e/E)>this.d.length-2};f.isNaN=function(){return!this.s};f.isNegative=f.isNeg=function(){return this.s<0};f.isPositive=f.isPos=function(){return this.s>0};f.isZero=function(){return!!this.d&&this.d[0]===0};f.lessThan=f.lt=function(e){return this.cmp(e)<0};f.lessThanOrEqualTo=f.lte=function(e){return this.cmp(e)<1};f.logarithm=f.log=function(e){var t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding,m=5;if(e==null)e=new c(10),t=!0;else{if(e=new c(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(r=u.d,u.s<0||!r||!r[0]||u.eq(1))return new c(r&&!r[0]?-1/0:u.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(b=!1,a=p+m,s=De(u,a),n=t?Wr(c,a+10):De(e,a),l=_(s,n,a,1),Gt(l.d,i=p,d))do if(a+=10,s=De(u,a),n=t?Wr(c,a+10):De(e,a),l=_(s,n,a,1),!o){+H(l.d).slice(i+1,i+15)+1==1e14&&(l=y(l,p+1,0));break}while(Gt(l.d,i+=10,d));return b=!0,y(l,p,d)};f.minus=f.sub=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,m=this,g=m.constructor;if(e=new g(e),!m.d||!e.d)return!m.s||!e.s?e=new g(NaN):m.d?e.s=-e.s:e=new g(e.d||m.s!==e.s?m:NaN),e;if(m.s!=e.s)return e.s=-e.s,m.plus(e);if(u=m.d,d=e.d,a=g.precision,l=g.rounding,!u[0]||!d[0]){if(d[0])e.s=-e.s;else if(u[0])e=new g(m);else return new g(l===3?-0:0);return b?y(e,a,l):e}if(r=K(e.e/E),c=K(m.e/E),u=u.slice(),o=c-r,o){for(p=o<0,p?(t=u,o=-o,s=d.length):(t=d,r=c,s=u.length),n=Math.max(Math.ceil(a/E),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=u.length,s=d.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(u[n]!=d[n]){p=u[n]<d[n];break}o=0}for(p&&(t=u,u=d,d=t,e.s=-e.s),s=u.length,n=d.length-s;n>0;--n)u[s++]=0;for(n=d.length;n>o;){if(u[--n]<d[n]){for(i=n;i&&u[--i]===0;)u[i]=de-1;--u[i],u[n]+=de}u[n]-=d[n]}for(;u[--s]===0;)u.pop();for(;u[0]===0;u.shift())--r;return u[0]?(e.d=u,e.e=Kr(u,r),b?y(e,a,l):e):new g(l===3?-0:0)};f.modulo=f.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?y(new n(r),n.precision,n.rounding):(b=!1,n.modulo==9?(t=_(r,e.abs(),0,3,1),t.s*=e.s):t=_(r,e,0,n.modulo,1),t=t.times(e),b=!0,r.minus(t))};f.naturalExponential=f.exp=function(){return pi(this)};f.naturalLogarithm=f.ln=function(){return De(this)};f.negated=f.neg=function(){var e=new this.constructor(this);return e.s=-e.s,y(e)};f.plus=f.add=function(e){var t,r,n,i,o,s,a,l,u,c,p=this,d=p.constructor;if(e=new d(e),!p.d||!e.d)return!p.s||!e.s?e=new d(NaN):p.d||(e=new d(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(u=p.d,c=e.d,a=d.precision,l=d.rounding,!u[0]||!c[0])return c[0]||(e=new d(p)),b?y(e,a,l):e;if(o=K(p.e/E),n=K(e.e/E),u=u.slice(),i=o-n,i){for(i<0?(r=u,i=-i,s=c.length):(r=c,n=o,s=u.length),o=Math.ceil(a/E),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=u.length,i=c.length,s-i<0&&(i=s,r=c,c=u,u=r),t=0;i;)t=(u[--i]=u[i]+c[i]+t)/de|0,u[i]%=de;for(t&&(u.unshift(t),++n),s=u.length;u[--s]==0;)u.pop();return e.d=u,e.e=Kr(u,n),b?y(e,a,l):e};f.precision=f.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(_e+e);return r.d?(t=xs(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};f.round=function(){var e=this,t=e.constructor;return y(new t(e),e.e+1,t.rounding)};f.sine=f.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=Yc(n,Cs(n,r)),n.precision=e,n.rounding=t,y(Ae>2?r.neg():r,e,t,!0)):new n(NaN)};f.squareRoot=f.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,u=s.s,c=s.constructor;if(u!==1||!a||!a[0])return new c(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);for(b=!1,u=Math.sqrt(+s),u==0||u==1/0?(t=H(a),(t.length+l)%2==0&&(t+="0"),u=Math.sqrt(t),l=K((l+1)/2)-(l<0||l%2),u==1/0?t="5e"+l:(t=u.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new c(t)):n=new c(u.toString()),r=(l=c.precision)+3;;)if(o=n,n=o.plus(_(s,o,r+2,1)).times(.5),H(o.d).slice(0,r)===(t=H(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(y(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(y(n,l+1,1),e=!n.times(n).eq(s));break}return b=!0,y(n,l,c.rounding,e)};f.tangent=f.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=_(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,y(Ae==2||Ae==4?r.neg():r,e,t,!0)):new n(NaN)};f.times=f.mul=function(e){var t,r,n,i,o,s,a,l,u,c=this,p=c.constructor,d=c.d,m=(e=new p(e)).d;if(e.s*=c.s,!d||!d[0]||!m||!m[0])return new p(!e.s||d&&!d[0]&&!m||m&&!m[0]&&!d?NaN:!d||!m?e.s/0:e.s*0);for(r=K(c.e/E)+K(e.e/E),l=d.length,u=m.length,l<u&&(o=d,d=m,m=o,s=l,l=u,u=s),o=[],s=l+u,n=s;n--;)o.push(0);for(n=u;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+m[n]*d[i-n-1]+t,o[i--]=a%de|0,t=a/de|0;o[i]=(o[i]+t)%de|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=Kr(o,r),b?y(e,p.precision,p.rounding):e};f.toBinary=function(e,t){return di(this,2,e,t)};f.toDecimalPlaces=f.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ie(e,0,Ne),t===void 0?t=n.rounding:ie(t,0,8),y(r,e+r.e+1,t))};f.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=ye(n,!0):(ie(e,0,Ne),t===void 0?t=i.rounding:ie(t,0,8),n=y(new i(n),e+1,t),r=ye(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=ye(i):(ie(e,0,Ne),t===void 0?t=o.rounding:ie(t,0,8),n=y(new o(i),e+i.e+1,t),r=ye(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};f.toFraction=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,m=this,g=m.d,h=m.constructor;if(!g)return new h(m);if(u=r=new h(1),n=l=new h(0),t=new h(n),o=t.e=xs(g)-m.e-1,s=o%E,t.d[0]=j(10,s<0?E+s:s),e==null)e=o>0?t:u;else{if(a=new h(e),!a.isInt()||a.lt(u))throw Error(_e+a);e=a.gt(t)?o>0?t:u:a}for(b=!1,a=new h(H(g)),c=h.precision,h.precision=o=g.length*E*2;p=_(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=u,u=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=_(e.minus(r),n,0,1,1),l=l.plus(i.times(u)),r=r.plus(i.times(n)),l.s=u.s=m.s,d=_(u,n,o,1).minus(m).abs().cmp(_(l,r,o,1).minus(m).abs())<1?[u,n]:[l,r],h.precision=c,b=!0,d};f.toHexadecimal=f.toHex=function(e,t){return di(this,16,e,t)};f.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:ie(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(b=!1,r=_(r,e,0,t,1).times(e),b=!0,y(r)):(e.s=r.s,r=e),r};f.toNumber=function(){return+this};f.toOctal=function(e,t){return di(this,8,e,t)};f.toPower=f.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,u=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(j(+a,u));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return y(a,n,o);if(t=K(e.e/E),t>=e.d.length-1&&(r=u<0?-u:u)<=Wc)return i=Ps(l,a,r,n),e.s<0?new l(1).div(i):y(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if((e.d[t]&1)==0&&(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=j(+a,u),t=r==0||!isFinite(r)?K(u*(Math.log("0."+H(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(b=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=pi(e.times(De(a,n+r)),n),i.d&&(i=y(i,n+5,1),Gt(i.d,n,o)&&(t=n+10,i=y(pi(e.times(De(a,t+r)),t),t+5,1),+H(i.d).slice(n+1,n+15)+1==1e14&&(i=y(i,n+1,0)))),i.s=s,b=!0,l.rounding=o,y(i,n,o))};f.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=ye(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(ie(e,1,Ne),t===void 0?t=i.rounding:ie(t,0,8),n=y(new i(n),e,t),r=ye(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toSignificantDigits=f.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ie(e,1,Ne),t===void 0?t=n.rounding:ie(t,0,8)),y(new n(r),e,t)};f.toString=function(){var e=this,t=e.constructor,r=ye(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};f.truncated=f.trunc=function(){return y(new this.constructor(this),this.e+1,1)};f.valueOf=f.toJSON=function(){var e=this,t=e.constructor,r=ye(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function H(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=E-n.length,r&&(o+=Oe(r)),o+=n;s=e[t],n=s+"",r=E-n.length,r&&(o+=Oe(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function ie(e,t,r){if(e!==~~e||e<t||e>r)throw Error(_e+e)}function Gt(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=E,i=0):(i=Math.ceil((t+1)/E),t%=E),o=j(10,E-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==j(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==j(10,t-3)-1,s}function Br(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=li.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function Kc(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/zr(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=at(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var _=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var u,c,p,d,m,g,h,R,T,C,w,k,ce,se,Nt,V,te,Pe,W,nt,Fr=n.constructor,Hn=n.s==i.s?1:-1,J=n.d,D=i.d;if(!J||!J[0]||!D||!D[0])return new Fr(!n.s||!i.s||(J?D&&J[0]==D[0]:!D)?NaN:J&&J[0]==0||!D?Hn*0:Hn/0);for(l?(m=1,c=n.e-i.e):(l=de,m=E,c=K(n.e/m)-K(i.e/m)),W=D.length,te=J.length,T=new Fr(Hn),C=T.d=[],p=0;D[p]==(J[p]||0);p++);if(D[p]>(J[p]||0)&&c--,o==null?(se=o=Fr.precision,s=Fr.rounding):a?se=o+(n.e-i.e)+1:se=o,se<0)C.push(1),g=!0;else{if(se=se/m+2|0,p=0,W==1){for(d=0,D=D[0],se++;(p<te||d)&&se--;p++)Nt=d*l+(J[p]||0),C[p]=Nt/D|0,d=Nt%D|0;g=d||p<te}else{for(d=l/(D[0]+1)|0,d>1&&(D=e(D,d,l),J=e(J,d,l),W=D.length,te=J.length),V=W,w=J.slice(0,W),k=w.length;k<W;)w[k++]=0;nt=D.slice(),nt.unshift(0),Pe=D[0],D[1]>=l/2&&++Pe;do d=0,u=t(D,w,W,k),u<0?(ce=w[0],W!=k&&(ce=ce*l+(w[1]||0)),d=ce/Pe|0,d>1?(d>=l&&(d=l-1),h=e(D,d,l),R=h.length,k=w.length,u=t(h,w,R,k),u==1&&(d--,r(h,W<R?nt:D,R,l))):(d==0&&(u=d=1),h=D.slice()),R=h.length,R<k&&h.unshift(0),r(w,h,k,l),u==-1&&(k=w.length,u=t(D,w,W,k),u<1&&(d++,r(w,W<k?nt:D,k,l))),k=w.length):u===0&&(d++,w=[0]),C[p++]=d,u&&w[0]?w[k++]=J[V]||0:(w=[J[V]],k=1);while((V++<te||w[0]!==void 0)&&se--);g=w[0]!==void 0}C[0]||C.shift()}if(m==1)T.e=c,hs=g;else{for(p=1,d=C[0];d>=10;d/=10)p++;T.e=p+c*m-1,y(T,a?o+T.e+1:o,s,g)}return T}}();function y(e,t,r,n){var i,o,s,a,l,u,c,p,d,m=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=E,s=t,c=p[d=0],l=c/j(10,i-s-1)%10|0;else if(d=Math.ceil((o+1)/E),a=p.length,d>=a)if(n){for(;a++<=d;)p.push(0);c=l=0,i=1,o%=E,s=o-E+1}else break e;else{for(c=a=p[d],i=1;a>=10;a/=10)i++;o%=E,s=o-E+i,l=s<0?0:c/j(10,i-s-1)%10|0}if(n=n||t<0||p[d+1]!==void 0||(s<0?c:c%j(10,i-s-1)),u=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?c/j(10,i-s):0:p[d-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,u?(t-=e.e+1,p[0]=j(10,(E-t%E)%E),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=d,a=1,d--):(p.length=d+1,a=j(10,E-o),p[d]=s>0?(c/j(10,i-s)%j(10,s)|0)*a:0),u)for(;;)if(d==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==de&&(p[0]=1));break}else{if(p[d]+=a,p[d]!=de)break;p[d--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return b&&(e.e>m.maxE?(e.d=null,e.e=NaN):e.e<m.minE&&(e.e=0,e.d=[0])),e}function ye(e,t,r){if(!e.isFinite())return Ts(e);var n,i=e.e,o=H(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Oe(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Oe(-i-1)+o,r&&(n=r-s)>0&&(o+=Oe(n))):i>=s?(o+=Oe(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Oe(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Oe(n))),o}function Kr(e,t){var r=e[0];for(t*=E;r>=10;r/=10)t++;return t}function Wr(e,t,r){if(t>Jc)throw b=!0,r&&(e.precision=r),Error(ys);return y(new e(Hr),t,1,!0)}function he(e,t,r){if(t>ci)throw Error(ys);return y(new e(Gr),t,r,!0)}function xs(e){var t=e.length-1,r=t*E+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function Oe(e){for(var t="";e--;)t+="0";return t}function Ps(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/E+4);for(b=!1;;){if(r%2&&(o=o.times(t),fs(o.d,s)&&(i=!0)),r=K(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),fs(t.d,s)}return b=!0,o}function ms(e){return e.d[e.d.length-1]&1}function vs(e,t,r){for(var n,i,o=new e(t[0]),s=0;++s<t.length;){if(i=new e(t[s]),!i.s){o=i;break}n=o.cmp(i),(n===r||n===0&&o.s===r)&&(o=i)}return o}function pi(e,t){var r,n,i,o,s,a,l,u=0,c=0,p=0,d=e.constructor,m=d.rounding,g=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(b=!1,l=g):l=t,a=new d(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(j(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new d(1),d.precision=l;;){if(o=y(o.times(e),l,1),r=r.times(++c),a=s.plus(_(o,r,l,1)),H(a.d).slice(0,l)===H(s.d).slice(0,l)){for(i=p;i--;)s=y(s.times(s),l,1);if(t==null)if(u<3&&Gt(s.d,l-n,m,u))d.precision=l+=10,r=o=a=new d(1),c=0,u++;else return y(s,d.precision=g,m,b=!0);else return d.precision=g,s}s=a}}function De(e,t){var r,n,i,o,s,a,l,u,c,p,d,m=1,g=10,h=e,R=h.d,T=h.constructor,C=T.rounding,w=T.precision;if(h.s<0||!R||!R[0]||!h.e&&R[0]==1&&R.length==1)return new T(R&&!R[0]?-1/0:h.s!=1?NaN:R?0:h);if(t==null?(b=!1,c=w):c=t,T.precision=c+=g,r=H(R),n=r.charAt(0),Math.abs(o=h.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=H(h.d),n=r.charAt(0),m++;o=h.e,n>1?(h=new T("0."+r),o++):h=new T(n+"."+r.slice(1))}else return u=Wr(T,c+2,w).times(o+""),h=De(new T(n+"."+r.slice(1)),c-g).plus(u),T.precision=w,t==null?y(h,w,C,b=!0):h;for(p=h,l=s=h=_(h.minus(1),h.plus(1),c,1),d=y(h.times(h),c,1),i=3;;){if(s=y(s.times(d),c,1),u=l.plus(_(s,new T(i),c,1)),H(u.d).slice(0,c)===H(l.d).slice(0,c))if(l=l.times(2),o!==0&&(l=l.plus(Wr(T,c+2,w).times(o+""))),l=_(l,new T(m),c,1),t==null)if(Gt(l.d,c-g,C,a))T.precision=c+=g,u=s=h=_(p.minus(1),p.plus(1),c,1),d=y(h.times(h),c,1),i=a=1;else return y(l,T.precision=w,C,b=!0);else return T.precision=w,l;l=u,i+=2}}function Ts(e){return String(e.s*e.s/0)}function Qr(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%E,r<0&&(n+=E),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=E;n<i;)e.d.push(+t.slice(n,n+=E));t=t.slice(n),n=E-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),b&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function zc(e,t){var r,n,i,o,s,a,l,u,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),bs.test(t))return Qr(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(Hc.test(t))r=16,t=t.toLowerCase();else if(Qc.test(t))r=2;else if(Gc.test(t))r=8;else throw Error(_e+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=Ps(n,new n(r),o,o*2)),u=Br(t,r,de),c=u.length-1,o=c;u[o]===0;--o)u.pop();return o<0?new n(e.s*0):(e.e=Kr(u,c),e.d=u,b=!1,s&&(e=_(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?j(2,l):He.pow(2,l))),b=!0,e)}function Yc(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:at(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/zr(5,r)),t=at(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function at(e,t,r,n,i){var o,s,a,l,u=1,c=e.precision,p=Math.ceil(c/E);for(b=!1,l=r.times(r),a=new e(n);;){if(s=_(a.times(l),new e(t++*t++),c,1),a=i?n.plus(s):n.minus(s),n=_(s.times(l),new e(t++*t++),c,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,u++}return b=!0,s.d.length=p+1,s}function zr(e,t){for(var r=e;--t;)r*=e;return r}function Cs(e,t){var r,n=t.s<0,i=he(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return Ae=n?4:1,t;if(r=t.divToInt(i),r.isZero())Ae=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return Ae=ms(r)?n?2:3:n?4:1,t;Ae=ms(r)?n?1:4:n?3:2}return t.minus(i).abs()}function di(e,t,r,n){var i,o,s,a,l,u,c,p,d,m=e.constructor,g=r!==void 0;if(g?(ie(r,1,Ne),n===void 0?n=m.rounding:ie(n,0,8)):(r=m.precision,n=m.rounding),!e.isFinite())c=Ts(e);else{for(c=ye(e),s=c.indexOf("."),g?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(c=c.replace(".",""),d=new m(1),d.e=c.length-s,d.d=Br(ye(d),10,i),d.e=d.d.length),p=Br(c,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])c=g?"0p+0":"0";else{if(s<0?o--:(e=new m(e),e.d=p,e.e=o,e=_(e,d,r,n,0,i),p=e.d,o=e.e,u=hs),s=p[r],a=i/2,u=u||p[r+1]!==void 0,u=n<4?(s!==void 0||u)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||u||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,u)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,c="";s<l;s++)c+=li.charAt(p[s]);if(g){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)c+="0";for(p=Br(c,i,t),l=p.length;!p[l-1];--l);for(s=1,c="1.";s<l;s++)c+=li.charAt(p[s])}else c=c.charAt(0)+"."+c.slice(1);c=c+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)c="0"+c;c="0."+c}else if(++o>l)for(o-=l;o--;)c+="0";else o<l&&(c=c.slice(0,o)+"."+c.slice(o))}c=(t==16?"0x":t==2?"0b":t==8?"0o":"")+c}return e.s<0?"-"+c:c}function fs(e,t){if(e.length>t)return e.length=t,!0}function Zc(e){return new this(e).abs()}function Xc(e){return new this(e).acos()}function ep(e){return new this(e).acosh()}function tp(e,t){return new this(e).plus(t)}function rp(e){return new this(e).asin()}function np(e){return new this(e).asinh()}function ip(e){return new this(e).atan()}function op(e){return new this(e).atanh()}function sp(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=he(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?he(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=he(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(_(e,t,o,1)),t=he(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(_(e,t,o,1)),r}function ap(e){return new this(e).cbrt()}function lp(e){return y(e=new this(e),e.e+1,2)}function up(e,t,r){return new this(e).clamp(t,r)}function cp(e){if(!e||typeof e!="object")throw Error(Jr+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,Ne,"rounding",0,8,"toExpNeg",-st,0,"toExpPos",0,st,"maxE",0,st,"minE",-st,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=ui[r]),(n=e[r])!==void 0)if(K(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(_e+r+": "+n);if(r="crypto",i&&(this[r]=ui[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(ws);else this[r]=!1;else throw Error(_e+r+": "+n);return this}function pp(e){return new this(e).cos()}function dp(e){return new this(e).cosh()}function As(e){var t,r,n;function i(o){var s,a,l,u=this;if(!(u instanceof i))return new i(o);if(u.constructor=i,gs(o)){u.s=o.s,b?!o.d||o.e>i.maxE?(u.e=NaN,u.d=null):o.e<i.minE?(u.e=0,u.d=[0]):(u.e=o.e,u.d=o.d.slice()):(u.e=o.e,u.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){u.s=1/o<0?-1:1,u.e=0,u.d=[0];return}if(o<0?(o=-o,u.s=-1):u.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;b?s>i.maxE?(u.e=NaN,u.d=null):s<i.minE?(u.e=0,u.d=[0]):(u.e=s,u.d=[o]):(u.e=s,u.d=[o]);return}if(o*0!==0){o||(u.s=NaN),u.e=NaN,u.d=null;return}return Qr(u,o.toString())}if(l==="string")return(a=o.charCodeAt(0))===45?(o=o.slice(1),u.s=-1):(a===43&&(o=o.slice(1)),u.s=1),bs.test(o)?Qr(u,o):zc(u,o);if(l==="bigint")return o<0?(o=-o,u.s=-1):u.s=1,Qr(u,o.toString());throw Error(_e+o)}if(i.prototype=f,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=cp,i.clone=As,i.isDecimal=gs,i.abs=Zc,i.acos=Xc,i.acosh=ep,i.add=tp,i.asin=rp,i.asinh=np,i.atan=ip,i.atanh=op,i.atan2=sp,i.cbrt=ap,i.ceil=lp,i.clamp=up,i.cos=pp,i.cosh=dp,i.div=mp,i.exp=fp,i.floor=gp,i.hypot=hp,i.ln=yp,i.log=wp,i.log10=bp,i.log2=Ep,i.max=xp,i.min=Pp,i.mod=vp,i.mul=Tp,i.pow=Cp,i.random=Ap,i.round=Sp,i.sign=Rp,i.sin=kp,i.sinh=Ip,i.sqrt=Op,i.sub=Dp,i.sum=_p,i.tan=Np,i.tanh=Mp,i.trunc=Fp,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function mp(e,t){return new this(e).div(t)}function fp(e){return new this(e).exp()}function gp(e){return y(e=new this(e),e.e+1,3)}function hp(){var e,t,r=new this(0);for(b=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return b=!0,new this(1/0);r=t}return b=!0,r.sqrt()}function gs(e){return e instanceof He||e&&e.toStringTag===Es||!1}function yp(e){return new this(e).ln()}function wp(e,t){return new this(e).log(t)}function Ep(e){return new this(e).log(2)}function bp(e){return new this(e).log(10)}function xp(){return vs(this,arguments,-1)}function Pp(){return vs(this,arguments,1)}function vp(e,t){return new this(e).mod(t)}function Tp(e,t){return new this(e).mul(t)}function Cp(e,t){return new this(e).pow(t)}function Ap(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:ie(e,1,Ne),n=Math.ceil(e/E),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(ws);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=E,n&&e&&(i=j(10,E-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=E)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<E&&(r-=E-n)}return s.e=r,s.d=a,s}function Sp(e){return y(e=new this(e),e.e+1,this.rounding)}function Rp(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function kp(e){return new this(e).sin()}function Ip(e){return new this(e).sinh()}function Op(e){return new this(e).sqrt()}function Dp(e,t){return new this(e).sub(t)}function _p(){var e=0,t=arguments,r=new this(t[e]);for(b=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return b=!0,y(r,this.precision,this.rounding)}function Np(e){return new this(e).tan()}function Mp(e){return new this(e).tanh()}function Fp(e){return y(e=new this(e),e.e+1,1)}f[Symbol.for("nodejs.util.inspect.custom")]=f.toString;f[Symbol.toStringTag]="Decimal";var He=f.constructor=As(ui);Hr=new He(Hr);Gr=new He(Gr);var ae=He;function Wt(e){return e===null?e:Array.isArray(e)?e.map(Wt):typeof e=="object"?Lp(e)?$p(e):typeof e=="bigint"||e instanceof Date||e instanceof Uint8Array||e instanceof ae?e:ot(e,Wt):e}function Lp(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function $p({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new ae(t);case"Json":return JSON.parse(t);default:pe(t,"Unknown tagged value")}}var we=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function Me(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function Ss(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}function Jt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function Vp(e){return{models:mi(e.models),enums:mi(e.enums),types:mi(e.types)}}function mi(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function lt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function Yr(e){return e.toString()!=="Invalid Date"}function ut(e){return He.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var Zr={};Ft(Zr,{ModelAction:()=>ct,datamodelEnumToSchemaEnum:()=>qp});function qp(e){return{name:e.name,values:e.values.map(t=>t.name)}}var ct=(w=>(w.findUnique="findUnique",w.findUniqueOrThrow="findUniqueOrThrow",w.findFirst="findFirst",w.findFirstOrThrow="findFirstOrThrow",w.findMany="findMany",w.create="create",w.createMany="createMany",w.createManyAndReturn="createManyAndReturn",w.update="update",w.updateMany="updateMany",w.updateManyAndReturn="updateManyAndReturn",w.upsert="upsert",w.delete="delete",w.deleteMany="deleteMany",w.groupBy="groupBy",w.count="count",w.aggregate="aggregate",w.findRaw="findRaw",w.aggregateRaw="aggregateRaw",w))(ct||{});var Os=ge(zo());import Hp from"node:fs";var Rs={keyword:Te,entity:Te,value:e=>z(Qe(e)),punctuation:Qe,directive:Te,function:Te,variable:e=>z(Qe(e)),string:e=>z(Lt(e)),boolean:Be,number:Te,comment:$t};var jp=e=>e,Xr={},Up=0,v={manual:Xr.Prism&&Xr.Prism.manual,disableWorkerMessageHandler:Xr.Prism&&Xr.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof me){let t=e;return new me(t.type,v.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(v.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++Up}),e.__id},clone:function e(t,r){let n,i,o=v.util.type(t);switch(r=r||{},o){case"Object":if(i=v.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=v.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=v.util.clone(v.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||v.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,v.languages.DFS(v.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=v.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=v.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return v.hooks.run("before-tokenize",n),n.tokens=v.tokenize(n.code,n.grammar),v.hooks.run("after-tokenize",n),me.stringify(v.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let h in r){if(!r.hasOwnProperty(h)||!r[h])continue;if(h==s)return;let R=r[h];R=v.util.type(R)==="Array"?R:[R];for(let T=0;T<R.length;++T){let C=R[T],w=C.inside,k=!!C.lookbehind,ce=!!C.greedy,se=0,Nt=C.alias;if(ce&&!C.pattern.global){let V=C.pattern.toString().match(/[imuy]*$/)[0];C.pattern=RegExp(C.pattern.source,V+"g")}C=C.pattern||C;for(let V=n,te=i;V<t.length;te+=t[V].length,++V){let Pe=t[V];if(t.length>e.length)return;if(Pe instanceof me)continue;if(ce&&V!=t.length-1){C.lastIndex=te;var p=C.exec(e);if(!p)break;var c=p.index+(k?p[1].length:0),d=p.index+p[0].length,a=V,l=te;for(let D=t.length;a<D&&(l<d||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,c>=l&&(++V,te=l);if(t[V]instanceof me)continue;u=a-V,Pe=e.slice(te,l),p.index-=te}else{C.lastIndex=0;var p=C.exec(Pe),u=1}if(!p){if(o)break;continue}k&&(se=p[1]?p[1].length:0);var c=p.index+se,p=p[0].slice(se),d=c+p.length,m=Pe.slice(0,c),g=Pe.slice(d);let W=[V,u];m&&(++V,te+=m.length,W.push(m));let nt=new me(h,w?v.tokenize(p,w):p,Nt,p,ce);if(W.push(nt),g&&W.push(g),Array.prototype.splice.apply(t,W),u!=1&&v.matchGrammar(e,t,r,V,te,!0,h),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return v.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=v.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=v.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:me};v.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};v.languages.javascript=v.languages.extend("clike",{"class-name":[v.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});v.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;v.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:v.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:v.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:v.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:v.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});v.languages.markup&&v.languages.markup.tag.addInlined("script","javascript");v.languages.js=v.languages.javascript;v.languages.typescript=v.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});v.languages.ts=v.languages.typescript;function me(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}me.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return me.stringify(r,t)}).join(""):Bp(e.type)(e.content)};function Bp(e){return Rs[e]||jp}function ks(e){return Qp(e,v.languages.javascript)}function Qp(e,t){return v.tokenize(e,t).map(n=>me.stringify(n)).join("")}function Is(e){return zn(e)}var en=class e{firstLineNumber;lines;static read(t){let r;try{r=Hp.readFileSync(t,"utf-8")}catch{return null}return e.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new e(1,r)}constructor(t,r){this.firstLineNumber=t,this.lines=r}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new e(this.firstLineNumber,i)}mapLines(t){return new e(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new e(t,Is(n).split(`
`))}highlight(){let t=ks(this.toString());return new e(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var Gp={red:ve,gray:$t,dim:Ue,bold:z,underline:re,highlightSource:e=>e.highlight()},Wp={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function Jp({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function Kp({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s=Jp({message:t,originalMethod:r,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),u=en.read(a.fileName)?.slice(l,a.lineNumber),c=u?.lineAt(a.lineNumber);if(u&&c){let p=Yp(c),d=zp(c);if(!d)return s;s.functionName=`${d.code})`,s.location=a,n||(u=u.mapLineAt(a.lineNumber,g=>g.slice(0,d.openingBraceIndex))),u=o.highlightSource(u);let m=String(u.lastLineNumber).length;if(s.contextLines=u.mapLines((g,h)=>o.gray(String(h).padStart(m))+" "+g).mapLines(g=>o.dim(g)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let g=p+m+1;g+=2,s.callArguments=(0,Os.default)(i,g).slice(g)}}return s}function zp(e){let t=Object.keys(ct).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function Yp(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function Zp({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(Xp(t))),i){a.push("");let u=[i.toString()];o&&(u.push(o),u.push(s.dim(")"))),a.push(u.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function Xp(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function tn(e){let t=e.showColors?Gp:Wp,r;return r=Kp(e,t),Zp(r,t)}var qs=ge(fi());function Ms(e,t,r){let n=Fs(e),i=ed(n),o=rd(i);o?rn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Fs(e){return e.errors.flatMap(t=>t.kind==="Union"?Fs(t):[t])}function ed(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:td(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function td(e,t){return[...new Set(e.concat(t))]}function rd(e){return ai(e,(t,r)=>{let n=_s(t),i=_s(r);return n!==i?n-i:Ns(t)-Ns(r)})}function _s(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Ns(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var le=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};$s();var pt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};Ls();var nn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};var on=e=>e,sn={bold:on,red:on,green:on,dim:on,enabled:!1},Vs={bold:z,red:ve,green:Lt,dim:Ue,enabled:!0},dt={write(e){e.writeLine(",")}};var Ee=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var Fe=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var mt=class extends Fe{items=[];addItem(t){return this.items.push(new nn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Ee("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(dt,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var ft=class e extends Fe{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof mt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Ee("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(dt,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};var U=class extends Fe{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Ee(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};var Kt=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(dt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function rn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":nd(e,t);break;case"IncludeOnScalar":id(e,t);break;case"EmptySelection":od(e,t,r);break;case"UnknownSelectionField":ud(e,t);break;case"InvalidSelectionValue":cd(e,t);break;case"UnknownArgument":pd(e,t);break;case"UnknownInputField":dd(e,t);break;case"RequiredArgumentMissing":md(e,t);break;case"InvalidArgumentType":fd(e,t);break;case"InvalidArgumentValue":gd(e,t);break;case"ValueTooLarge":hd(e,t);break;case"SomeFieldsMissing":yd(e,t);break;case"TooManyFieldsGiven":wd(e,t);break;case"Union":Ms(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function nd(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function id(e,t){let[r,n]=zt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new le(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${Yt(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function od(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){sd(e,t,i);return}if(n.hasField("select")){ad(e,t);return}}if(r?.[Me(e.outputType.name)]){ld(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function sd(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new le(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function ad(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Bs(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${Yt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function ld(e,t){let r=new Kt;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new le("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=zt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let l=a?.value.asObject()??new ft;l.addSuggestion(n),a.value=l}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function ud(e,t){let r=Qs(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Bs(n,e.outputType);break;case"include":Ed(n,e.outputType);break;case"omit":bd(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(Yt(n)),i.join(" ")})}function cd(e,t){let r=Qs(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function pd(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),xd(n,e.arguments)),t.addErrorMessage(i=>js(i,r,e.arguments.map(o=>o.name)))}function dd(e,t){let[r,n]=zt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Hs(o,e.inputType)}t.addErrorMessage(o=>js(o,n,e.inputType.fields.map(s=>s.name)))}function js(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=vd(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(Yt(e)),n.join(" ")}function md(e,t){let r;t.addErrorMessage(l=>r?.value instanceof U&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=zt(e.argumentPath),s=new Kt,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new le(o,s).makeRequired())}else{let l=e.inputTypes.map(Us).join(" | ");a.addSuggestion(new le(o,l).makeRequired())}}function Us(e){return e.kind==="list"?`${Us(e.elementType)}[]`:e.name}function fd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=an("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function gd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=an("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function hd(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof U&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function yd(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Hs(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${an("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Yt(i)),o.join(" ")})}function wd(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${an("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Bs(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new le(r.name,"true"))}function Ed(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new le(r.name,"true"))}function bd(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new le(r.name,"true"))}function xd(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new le(r.name,r.typeNames.join(" | ")))}function Qs(e,t){let[r,n]=zt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),l=o?.getField(n);return o&&l?{parentKind:"select",parent:o,field:l,fieldName:n}:(l=s?.getField(n),s&&l?{parentKind:"include",field:l,parent:s,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"omit",field:l,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Hs(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new le(r.name,r.typeNames.join(" | ")))}function zt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function Yt({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function an(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Pd=3;function vd(e,t){let r=1/0,n;for(let i of t){let o=(0,qs.default)(e,i);o>Pd||o<r&&(r=o,n=i)}return n}var Zt=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function gt(e){return e instanceof Zt}var ln=Symbol(),hi=new WeakMap,Se=class{constructor(t){t===ln?hi.set(this,`Prisma.${this._getName()}`):hi.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return hi.get(this)}},Xt=class extends Se{_getNamespace(){return"NullTypes"}},er=class extends Xt{#e};wi(er,"DbNull");var tr=class extends Xt{#e};wi(tr,"JsonNull");var rr=class extends Xt{#e};wi(rr,"AnyNull");var yi={classes:{DbNull:er,JsonNull:tr,AnyNull:rr},instances:{DbNull:new er(ln),JsonNull:new tr(ln),AnyNull:new rr(ln)}};function wi(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Gs=": ",un=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Gs.length}write(t){let r=new Ee(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Gs).write(this.value)}};var Ei=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function ht(e){return new Ei(Ws(e))}function Ws(e){let t=new ft;for(let[r,n]of Object.entries(e)){let i=new un(r,Js(n));t.addField(i)}return t}function Js(e){if(typeof e=="string")return new U(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new U(String(e));if(typeof e=="bigint")return new U(`${e}n`);if(e===null)return new U("null");if(e===void 0)return new U("undefined");if(ut(e))return new U(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new U(`Buffer.alloc(${e.byteLength})`):new U(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=Yr(e)?e.toISOString():"Invalid Date";return new U(`new Date("${t}")`)}return e instanceof Se?new U(`Prisma.${e._getName()}`):gt(e)?new U(`prisma.${Me(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Td(e):typeof e=="object"?Ws(e):new U(Object.prototype.toString.call(e))}function Td(e){let t=new mt;for(let r of e)t.addItem(Js(r));return t}function cn(e,t){let r=t==="pretty"?Vs:sn,n=e.renderAllMessages(r),i=new pt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function pn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=ht(e);for(let p of t)rn(p,a,s);let{message:l,args:u}=cn(a,r),c=tn({message:l,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:u});throw new Z(c,{clientVersion:o})}function be(e){return e.replace(/^./,t=>t.toLowerCase())}function zs(e,t,r){let n=be(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Cd({...e,...Ks(t.name,e,t.result.$allModels),...Ks(t.name,e,t.result[n])})}function Cd(e){let t=new we,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return ot(e,n=>({...n,needs:r(n.name,new Set)}))}function Ks(e,t,r){return r?ot(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Ad(t,o,i)})):{}}function Ad(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Ys(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function Zs(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var dn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new we;modelExtensionsCache=new we;queryCallbacksCache=new we;clientExtensions=Jt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=Jt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>zs(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=be(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},yt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new dn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new dn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var mn=class{constructor(t){this.name=t}};function Xs(e){return e instanceof mn}function Sd(e){return new mn(e)}var ea=Symbol(),nr=class{constructor(t){if(t!==ea)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?bi:t}},bi=new nr(ea);function xe(e){return e instanceof nr}var Rd={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},ta="explicitly `undefined` values are not allowed";function Pi({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=yt.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c}){let p=new xi({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c});return{modelName:e,action:Rd[t],query:ir(r,p)}}function ir({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:na(r,n),selection:kd(e,t,i,n)}}function kd(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),_d(e,n)):Id(n,t,r)}function Id(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&Od(n,t,e),Dd(n,r,e),n}function Od(e,t,r){for(let[n,i]of Object.entries(t)){if(xe(i))continue;let o=r.nestSelection(n);if(vi(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=ir(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=ir(i,o)}}function Dd(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=Zs(i,n);for(let[s,a]of Object.entries(o)){if(xe(a))continue;vi(a,r.nestSelection(s));let l=r.findField(s);n?.[s]&&!l||(e[s]=!a)}}function _d(e,t){let r={},n=t.getComputedFields(),i=Ys(e,n);for(let[o,s]of Object.entries(i)){if(xe(s))continue;let a=t.nestSelection(o);vi(s,a);let l=t.findField(o);if(!(n?.[o]&&!l)){if(s===!1||s===void 0||xe(s)){r[o]=!1;continue}if(s===!0){l?.kind==="object"?r[o]=ir({},a):r[o]=!0;continue}r[o]=ir(s,a)}}return r}function ra(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(lt(e)){if(Yr(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(Xs(e))return{$type:"Param",value:e.name};if(gt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Nd(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:Buffer.from(r,n,i).toString("base64")}}if(Md(e))return e.values;if(ut(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Se){if(e!==yi.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Fd(e))return e.toJSON();if(typeof e=="object")return na(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function na(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);xe(i)||(i!==void 0?r[n]=ra(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:ta}))}return r}function Nd(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||xe(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(ra(o,i))}return r}function Md(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Fd(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function vi(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:ta})}var xi=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){pn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Me(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:pe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function ia(e){if(!e._hasPreviewFlag("metrics"))throw new Z("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var or=class{_client;constructor(t){this._client=t}prometheus(t){return ia(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return ia(this._client),this._client._engine.metrics({format:"json",...t})}};function Ld(e,t){let r=Jt(()=>$d(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function $d(e){return{datamodel:{models:Ti(e.models),enums:Ti(e.enums),types:Ti(e.types)}}}function Ti(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}var Ci=new WeakMap,fn="$$PrismaTypedSql",sr=class{constructor(t,r){Ci.set(this,{sql:t,values:r}),Object.defineProperty(this,fn,{value:fn})}get sql(){return Ci.get(this).sql}get values(){return Ci.get(this).values}};function Vd(e){return(...t)=>new sr(e,t)}function gn(e){return e!=null&&e[fn]===fn}var Hu=ge(oa());import{AsyncResource as xg}from"node:async_hooks";import{EventEmitter as Pg}from"node:events";import vg from"node:fs";import So from"node:path";var ue=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function jd(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ue([r,...Array(e.length-1).fill(t),n],e)}function sa(e){return new ue([e],[])}var Ud=sa("");function aa(e,...t){return new ue(e,t)}function ar(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function X(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}function Ge(e){let t=new we;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var hn={enumerable:!0,configurable:!0,writable:!0};function yn(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>hn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var la=Symbol.for("nodejs.util.inspect.custom");function fe(e,t){let r=Bd(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=ua(Reflect.ownKeys(o),r),a=ua(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...hn,...l?.getPropertyDescriptor(s)}:hn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[la]=function(){let o={...this};return delete o[la],o},i}function Bd(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function ua(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function wt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function Et(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function ca(e){if(e===void 0)return"";let t=ht(e);return new pt(0,{colors:sn}).write(t).toString()}var Qd="P2037";function wn({error:e,user_facing_error:t},r,n){return t.error_code?new Q(Hd(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new Y(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function Hd(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Qd&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}var lr="<unknown>";function pa(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=Jd(n)||zd(n)||Xd(n)||nm(n)||tm(n);return i&&r.push(i),r},[])}var Gd=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Wd=/\((\S*)(?::(\d+))(?::(\d+))\)/;function Jd(e){var t=Gd.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=Wd.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||lr,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var Kd=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function zd(e){var t=Kd.exec(e);return t?{file:t[2],methodName:t[1]||lr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Yd=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,Zd=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function Xd(e){var t=Yd.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=Zd.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||lr,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var em=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function tm(e){var t=em.exec(e);return t?{file:t[3],methodName:t[1]||lr,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var rm=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function nm(e){var t=rm.exec(e);return t?{file:t[2],methodName:t[1]||lr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Ai=class{getLocation(){return null}},Si=class{_error;constructor(){this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=pa(t).find(i=>{if(!i.file)return!1;let o=ei(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function Le(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Ai:new Si}var da={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function bt(e={}){let t=om(e);return Object.entries(t).reduce((n,[i,o])=>(da[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function om(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function En(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function ma(e,t){let r=En(e);return t({action:"aggregate",unpacker:r,argsMapper:bt})(e)}function sm(e={}){let{select:t,...r}=e;return typeof t=="object"?bt({...r,_count:t}):bt({...r,_count:{_all:!0}})}function am(e={}){return typeof e.select=="object"?t=>En(e)(t)._count:t=>En(e)(t)._count._all}function fa(e,t){return t({action:"count",unpacker:am(e),argsMapper:sm})(e)}function lm(e={}){let t=bt(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function um(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function ga(e,t){return t({action:"groupBy",unpacker:um(e),argsMapper:lm})(e)}function ha(e,t,r){if(t==="aggregate")return n=>ma(n,r);if(t==="count")return n=>fa(n,r);if(t==="groupBy")return n=>ga(n,r)}function ya(e,t){let r=t.fields.filter(i=>!i.relationName),n=Ss(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new Zt(e,o,s.type,s.isList,s.kind==="enum")},...yn(Object.keys(n))})}var wa=e=>Array.isArray(e)?e:e.split("."),Ri=(e,t)=>wa(t).reduce((r,n)=>r&&r[n],e),Ea=(e,t,r)=>wa(t).reduceRight((n,i,o,s)=>Object.assign({},Ri(e,s.slice(0,o)),{[i]:n}),r);function cm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function pm(e,t,r){return t===void 0?e??{}:Ea(t,r,e||!0)}function ki(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,u)=>({...l,[u.name]:u}),{});return l=>{let u=Le(e._errorFormat),c=cm(n,i),p=pm(l,o,c),d=r({dataPath:c,callsite:u})(p),m=dm(e,t);return new Proxy(d,{get(g,h){if(!m.includes(h))return g[h];let T=[a[h].type,r,h],C=[c,p];return ki(e,...T,...C)},...yn([...m,...Object.getOwnPropertyNames(d)])})}}function dm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var mm=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],fm=["aggregate","count","groupBy"];function Ii(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[gm(e,t),ym(e,t),ar(r),X("name",()=>t),X("$name",()=>t),X("$parent",()=>e._appliedParent)];return fe({},n)}function gm(e,t){let r=be(t),n=Object.keys(ct).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>l=>{let u=Le(e._errorFormat);return e._createPrismaPromise(c=>{let p={args:l,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:c,callsite:u};return e._request({...p,...a})},{action:o,args:l,model:t})};return mm.includes(o)?ki(e,t,s):hm(i)?ha(e,i,s):s({})}}}function hm(e){return fm.includes(e)}function ym(e,t){return Ge(X("fields",()=>{let r=e._runtimeDataModel.models[t];return ya(t,r)}))}function ba(e){return e.replace(/^./,t=>t.toUpperCase())}var Oi=Symbol();function ur(e){let t=[wm(e),Em(e),X(Oi,()=>e),X("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(ar(r)),fe(e,t)}function wm(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function Em(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(be),n=[...new Set(t.concat(r))];return Ge({getKeys(){return n},getPropertyValue(i){let o=ba(i);if(e._runtimeDataModel.models[o]!==void 0)return Ii(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Ii(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function xa(e){return e[Oi]?e[Oi]:e}function Pa(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return ur(t)}function va({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let l of Object.values(o)){if(n){if(n[l.name])continue;let u=l.needs.filter(c=>n[c]);u.length>0&&a.push(wt(u))}else if(r){if(!r[l.name])continue;let u=l.needs.filter(c=>!r[c]);u.length>0&&a.push(wt(u))}bm(e,l.needs)&&s.push(xm(l,fe(e,s)))}return s.length>0||a.length>0?fe(e,[...s,...a]):e}function bm(e,t){return t.every(r=>si(e,r))}function xm(e,t){return Ge(X(e.name,()=>e.compute(t)))}function bn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=bn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Ta({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Ta({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Ta({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||xe(s))continue;let l=n.models[r].fields.find(c=>c.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let u=typeof s=="object"?s:{};t[o]=bn({visitor:i,result:t[o],args:u,modelName:l.type,runtimeDataModel:n})}}function Ca({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:bn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,l,u)=>{let c=be(l);return va({result:a,modelName:c,select:u.select,omit:u.select?void 0:{...o?.[c],...u.omit},extensions:n})}})}var Pm=["$connect","$disconnect","$on","$transaction","$use","$extends"],Aa=Pm;function Sa(e){if(e instanceof ue)return vm(e);if(gn(e))return Tm(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=cr(e[n]);return r}let t={};for(let r in e)t[r]=cr(e[r]);return t}function vm(e){return new ue(e.strings,e.values)}function Tm(e){return new sr(e.sql,e.values)}function cr(e){if(typeof e!="object"||e==null||e instanceof Se||gt(e))return e;if(ut(e))return new ae(e.toFixed());if(lt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=cr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:cr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=cr(e[r]);return t}pe(e,"Unknown value")}function ka(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Sa(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=_a(o,l),a.args=s,ka(e,a,r,n+1)}})})}function Ia(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return ka(e,t,s)}function Oa(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Da(r,n,0,e):e(r)}}function Da(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=_a(i,l),Da(a,t,r+1,n)}})}var Ra=e=>e;function _a(e=Ra,t=Ra){return r=>e(t(r))}var Na=L("prisma:client"),Ma={Vercel:"vercel","Netlify CI":"netlify"};function Fa({postinstall:e,ciName:t,clientVersion:r}){if(Na("checkPlatformCaching:postinstall",e),Na("checkPlatformCaching:ciName",t),e===!0&&t&&t in Ma){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${Ma[t]}-build`;throw console.error(n),new I(n,r)}}function La(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}var Cm=()=>globalThis.process?.release?.name==="node",Am=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,Sm=()=>!!globalThis.Deno,Rm=()=>typeof globalThis.Netlify=="object",km=()=>typeof globalThis.EdgeRuntime=="object",Im=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function Om(){return[[Rm,"netlify"],[km,"edge-light"],[Im,"workerd"],[Sm,"deno"],[Am,"bun"],[Cm,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var Dm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function Di(){let e=Om();return{id:e,prettyName:Dm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var _i=ge(Xn());function $a(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}function Va(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var qa=ge(cs());function ja({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,qa.default)({user:t,repo:r,template:n,title:e,body:i})}function Ua({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=jo(6e3-(s?.length??0)),l=Va((0,_i.default)(a)),u=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=(0,_i.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${u}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?$a(s):""}
\`\`\`
`),p=ja({title:r,body:c});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${re(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}var pr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(pr||(pr={}));function $(e,t){throw new Error(t)}function Ba(e){switch(e){case"postgres":return"postgresql";case"mysql":return"mysql";case"sqlite":return"sqlite";default:$(e,`Unknown provider: ${e}`)}}function Ni(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}var dr=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function Qa(e){if(!Ni(e))throw e;let t=_m(e),r=Nm(e);throw!t||!r?e:new dr(r,t,e)}function _m(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":return"P2023";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":return;default:$(e.cause,`Unknown error: ${e.cause}`)}}function Nm(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${Mi({fields:e.cause.fields})}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${Mi(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${Mi({fields:e.cause.fields})}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":return;default:$(e.cause,`Unknown error: ${e.cause}`)}}function Mi(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}function Ga(e,t){switch(t.type){case"Object":return Wa(e,t.fields);case"Value":return Fi(e,t.resultType);default:$(t,`Invalid data mapping type: '${t.type}'`)}}function Wa(e,t){if(e===null)return null;if(Array.isArray(e))return e.map(n=>Ha(n,t));if(typeof e=="object")return Ha(e,t);throw new Error(`DataMapper: Expected an array or an object, got: ${typeof e}`)}function Ha(e,t){if(typeof e!="object")throw new Error(`DataMapper: Expected an object, but got '${typeof e}'`);let r={};for(let[n,i]of Object.entries(t))switch(i.type){case"Object":if(Object.hasOwn(e,n))r[n]=Wa(e[n],i.fields);else throw new Error(`DataMapper: Missing data field (Object): '${n}'; node: ${JSON.stringify(i)}; data: ${JSON.stringify(e)}`);break;case"Value":{let o=i.dbName;if(Object.hasOwn(e,o))r[n]=Fi(e[o],i.resultType);else throw new Error(`DataMapper: Missing data field (Value): '${o}'; node: ${JSON.stringify(i)}; data: ${JSON.stringify(e)}`)}break;default:$(i,`DataMapper: Invalid data mapping node type: '${i.type}'`)}return r}function Fi(e,t){if(e===null)return null;switch(t.type){case"Any":return e;case"String":return typeof e=="string"?e:`${e}`;case"Int":return typeof e=="number"?e:parseInt(`${e}`,10);case"BigInt":return typeof e=="bigint"?e:BigInt(`${e}`);case"Float":return typeof e=="number"?e:parseFloat(`${e}`);case"Boolean":return typeof e=="boolean"?e:e!=="0";case"Decimal":return typeof e=="number"?new ae(e):new ae(`${e}`);case"Date":return e instanceof Date?e:new Date(`${e}`);case"Array":return e.map(n=>Fi(n,t.inner));case"Object":return typeof e=="string"?e:JSON.stringify(e);case"Bytes":{if(!Array.isArray(e))throw new Error(`DataMapper: Bytes data is invalid, got: ${typeof e}`);return new Uint8Array(e)}default:$(t,`DataMapper: Unknown result type: ${t.type}`)}}function We(e,t){var r="000000000"+e;return r.substr(r.length-t)}import Mm from"node:os";function Fm(){try{return Mm.hostname()}catch{return process.env._CLUSTER_NETWORK_NAME_||process.env.COMPUTERNAME||"hostname"}}var Ja=2,Lm=We(process.pid.toString(36),Ja),Ka=Fm(),$m=Ka.length,Vm=We(Ka.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+$m+36).toString(36),Ja);function Li(){return Lm+Vm}function xn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function $i(e){let n=Math.pow(36,4),i=0;function o(){return We((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var l="c",u=new Date().getTime().toString(36),c=We(s().toString(36),4),p=e(),d=o()+o();return l+u+c+p+d}return a.fingerprint=e,a.isCuid=xn,a}var qm=$i(Li);var za=qm;var Jl=ge(ql());import{webcrypto as Ul}from"node:crypto";var jl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var Sf=128,Ke,vt;function Rf(e){!Ke||Ke.length<e?(Ke=Buffer.allocUnsafe(e*Sf),Ul.getRandomValues(Ke),vt=0):vt+e>Ke.length&&(Ul.getRandomValues(Ke),vt=0),vt+=e}function Wi(e=21){Rf(e|=0);let t="";for(let r=vt-e;r<vt;r++)t+=jl[Ke[r]&63];return t}import Cn from"node:crypto";var Ql="0123456789ABCDEFGHJKMNPQRSTVWXYZ",wr=32;var kf=16,Hl=10,Bl=0xffffffffffff;var ze;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(ze||(ze={}));var Ye=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function If(e){let t=Math.floor(e()*wr);return t===wr&&(t=wr-1),Ql.charAt(t)}function Of(e){let t=Df(),r=t&&(t.crypto||t.msCrypto)||(typeof Cn<"u"?Cn:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(Cn?.randomBytes)return()=>Cn.randomBytes(1).readUInt8()/255;throw new Ye(ze.PRNGDetectFailure,"Failed to find a reliable PRNG")}function Df(){return Mf()?self:typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:null}function _f(e,t){let r="";for(;e>0;e--)r=If(t)+r;return r}function Nf(e,t=Hl){if(isNaN(e))throw new Ye(ze.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>Bl)throw new Ye(ze.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${Bl}: ${e}`);if(e<0)throw new Ye(ze.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new Ye(ze.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%wr,n=Ql.charAt(r)+n,e=(e-r)/wr;return n}function Mf(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function Gl(e,t){let r=t||Of(),n=!e||isNaN(e)?Date.now():e;return Nf(n,Hl)+_f(kf,r)}var G=[];for(let e=0;e<256;++e)G.push((e+256).toString(16).slice(1));function An(e,t=0){return(G[e[t+0]]+G[e[t+1]]+G[e[t+2]]+G[e[t+3]]+"-"+G[e[t+4]]+G[e[t+5]]+"-"+G[e[t+6]]+G[e[t+7]]+"-"+G[e[t+8]]+G[e[t+9]]+"-"+G[e[t+10]]+G[e[t+11]]+G[e[t+12]]+G[e[t+13]]+G[e[t+14]]+G[e[t+15]]).toLowerCase()}import{randomFillSync as Ff}from"node:crypto";var Rn=new Uint8Array(256),Sn=Rn.length;function Tt(){return Sn>Rn.length-16&&(Ff(Rn),Sn=0),Rn.slice(Sn,Sn+=16)}import{randomUUID as Lf}from"node:crypto";var Ji={randomUUID:Lf};function $f(e,t,r){if(Ji.randomUUID&&!t&&!e)return Ji.randomUUID();e=e||{};let n=e.random??e.rng?.()??Tt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return An(n)}var Ki=$f;var zi={};function Vf(e,t,r){let n;if(e)n=Wl(e.random??e.rng?.()??Tt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Tt();qf(zi,i,o),n=Wl(o,zi.msecs,zi.seq,t,r)}return t??An(n)}function qf(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Wl(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var Yi=Vf;var kn=class{#e={};constructor(){this.register("now",new In),this.register("uuid",new Zi),this.register("cuid",new Xi),this.register("ulid",new eo),this.register("nanoid",new to),this.register("product",new ro)}snapshot(){return Object.create(this.#e,{now:{value:new In}})}register(t,r){this.#e[t]=r}},In=class{#e=new Date;generate(){return this.#e.toISOString()}},Zi=class{generate(t){if(t===4)return Ki();if(t===7)return Yi();throw new Error("Invalid UUID generator arguments")}},Xi=class{generate(t){if(t===1)return za();if(t===2)return(0,Jl.createId)();throw new Error("Invalid CUID generator arguments")}},eo=class{generate(){return Gl()}},to=class{generate(t){if(typeof t=="number")return Wi(t);if(t===void 0)return Wi();throw new Error("Invalid Nanoid generator arguments")}},ro=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};function no(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function io(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function Kl(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function ao(e,t,r){let n=e.type;switch(n){case"rawSql":return Yl(e.sql,zl(e.params,t,r));case"templateSql":return jf(e.fragments,e.placeholderFormat,zl(e.params,t,r));default:$(n,"Invalid query type")}}function zl(e,t,r){return e.map(n=>so(n,t,r))}function so(e,t,r){let n=e;for(;Bf(n);)if(no(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(io(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>so(a,t,r)))}else $(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)&&(n=n.map(i=>so(i,t,r))),Kl(n)&&(n=Buffer.from(n.prisma__value,"base64")),n}function jf(e,t,r){let n=0,i=1,o=[],s=e.map(a=>{let l=a.type;switch(l){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),oo(t,i++);case"stringChunk":return a.value;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let u=r[n++],c=Array.isArray(u)?u:[u];return`(${c.length==0?"NULL":c.map(d=>(o.push(d),oo(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let u=r[n++];if(!Array.isArray(u))throw new Error("Malformed query template. Tuple list expected.");if(u.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return u.map(p=>{if(!Array.isArray(p))throw new Error("Malformed query template. Tuple expected.");return`(${p.map(m=>(o.push(m),oo(t,i++))).join(",")})`}).join(",")}default:$(l,"Invalid fragment type")}}).join("");return Yl(s,o)}function oo(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function Yl(e,t){let r=t.map(n=>Uf(n));return{sql:e,args:t,argTypes:r}}function Uf(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":Buffer.isBuffer(e)?"Bytes":"Unknown"}function Bf(e){return no(e)||io(e)}function Zl(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let a=0;a<o.length;a++){let l=o[a];a===o.length-1?s[l]=n:(s[l]===void 0&&(s[l]={}),s=s[l])}return r},{}))}function Xl(e,t,r){if(!t.every(n=>lo(e,n))){let n=Qf(e,r),i=Hf(r);throw new dr(n,i,r.context)}}function lo(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"never":return!1;default:$(t,`Unknown rule type: ${t.type}`)}}function Qf(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:0}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:$(t,`Unknown error identifier: ${t}`)}}function Hf(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:$(e,`Unknown error identifier: ${e}`)}}var Ct=class e{#e;#n;#t;#i=new kn;#r;#o;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o}){this.#e=t,this.#n=r,this.#t=n,this.#r=i,this.#o=o}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:Zl})}async run(t,r){return this.interpretNode(t,r,this.#n,this.#i.snapshot()).catch(n=>Qa(n))}async interpretNode(t,r,n,i){switch(t.type){case"seq":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i)));return o[o.length-1]}case"get":return n[t.args.name];case"let":{let o=Object.create(n);for(let s of t.args.bindings)o[s.name]=await this.interpretNode(s.expr,r,o,i);return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!eu(s))return s}return[]}case"concat":return(await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i)))).reduce((s,a)=>s.concat(uo(a)),[]);case"sum":return(await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i)))).reduce((s,a)=>tu(s)+tu(a));case"execute":{let o=ao(t.args,n,i);return this.#s(o,r,async()=>await r.executeRaw(o))}case"query":{let o=ao(t.args,n,i);return this.#s(o,r,async()=>this.#o(await r.queryRaw(o)))}case"reverse":{let o=await this.interpretNode(t.args,r,n,i);return Array.isArray(o)?o.reverse():o}case"unique":{let o=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return o;if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return o[0]??null}case"required":{let o=await this.interpretNode(t.args,r,n,i);if(eu(o))throw new Error("Required value is empty");return o}case"mapField":{let o=await this.interpretNode(t.args.records,r,n,i);return iu(o,t.args.field)}case"join":{let o=await this.interpretNode(t.args.parent,r,n,i),s=await Promise.all(t.args.children.map(async a=>({joinExpr:a,childRecords:await this.interpretNode(a.child,r,n,i)})));if(Array.isArray(o)){for(let a of o)ru(On(a),s);return o}return ru(On(o),s)}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let l=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),l}catch(l){throw await o.rollbackTransaction(s.id),l}}case"dataMap":{let o=await this.interpretNode(t.args.expr,r,n,i);return Ga(o,t.args.structure)}case"validate":{let o=await this.interpretNode(t.args.expr,r,n,i);return Xl(o,t.args.rules,t.args),o}case"if":{let o=await this.interpretNode(t.args.value,r,n,i);return lo(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return;case"diff":{let o=await this.interpretNode(t.args.from,r,n,i),s=await this.interpretNode(t.args.to,r,n,i),a=new Set(uo(s));return uo(o).filter(l=>!a.has(l))}default:$(t,`Unexpected node type: ${t.type}`)}}#s(t,r,n){return this.#r.runInChildSpan({name:"db_query",kind:pr.CLIENT,attributes:{"db.query.text":t.sql,"db.system.name":Ba(r.provider)}},async()=>{let i=new Date,o=performance.now(),s=await n(),a=performance.now();return this.#t?.({timestamp:i,duration:a-o,query:t.sql,params:t.args}),s})}};function eu(e){return Array.isArray(e)?e.length===0:e==null}function uo(e){return Array.isArray(e)?e:[e]}function tu(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function On(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function iu(e,t){return Array.isArray(e)?e.map(r=>iu(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function ru(e,t){for(let{joinExpr:r,childRecords:n}of t)e[r.parentField]=Gf(n,e,r);return e}function Gf(e,t,r){if(Array.isArray(e))return e.filter(n=>nu(On(n),t,r));if(e===null)return null;{let n=On(e);return nu(n,t,r)?n:null}}function nu(e,t,r){for(let[n,i]of r.on)if(t[n]!==e[i])return!1;return!0}async function Wf(){return globalThis.crypto??await import("node:crypto")}async function ou(){return(await Wf()).randomUUID()}var ee=class extends Error{constructor(r,n){super("Transaction API error: "+r);this.meta=n}code="P2028"},Er=class extends ee{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Dn=class extends ee{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction`)}},_n=class extends ee{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back`)}},Nn=class extends ee{constructor(){super("Unable to start a transaction in the given time.")}},Mn=class extends ee{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},At=class extends ee{constructor(t){super(`Internal Consistency Error: ${t}`)}},Fn=class extends ee{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var Jf=100,br=L("prisma:client:transactionManager"),Kf=()=>({sql:"COMMIT",args:[],argTypes:[]}),zf=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),xr=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#e(t))}async#e(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await ou(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n),n.timer=this.startTransactionTimeout(n.id,r.maxWait);let i=await this.driverAdapter.startTransaction(r.isolationLevel);switch(n.status){case"waiting":return n.transaction=i,clearTimeout(n.timer),n.timer=void 0,n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"timed_out":throw new Nn;case"running":case"committed":case"rolled_back":throw new At(`Transaction in invalid state ${n.status} although it just finished startup.`);default:$(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Er;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(br("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"waiting":case"running":throw new At("Active transaction found in closed transactions list.");case"committed":throw new Dn(r);case"rolled_back":throw new _n(r);case"timed_out":throw new Mn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw br("Transaction not found.",t),new Er}if(["committed","rolled_back","timed_out"].includes(n.status))throw new At("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{br("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):br("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){br("Closing transaction.",{transactionId:t.id,status:r}),t.status=r,t.transaction&&r==="committed"?(await t.transaction.commit(),t.transaction.options.usePhantomQuery||await t.transaction.executeRaw(Kf())):t.transaction&&(await t.transaction.rollback(),t.transaction.options.usePhantomQuery||await t.transaction.executeRaw(zf())),clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>Jf&&this.closedTransactions.shift()}validateOptions(t){if(!t.timeout)throw new ee("timeout is required");if(!t.maxWait)throw new ee("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new Fn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}};var Ln="6.8.2";var co,su={async loadQueryCompiler(e){let{clientVersion:t,adapter:r,compilerWasm:n}=e;if(r===void 0)throw new I(`The \`adapter\` option for \`PrismaClient\` is required in this context (${Di().prettyName})`,t);if(n===void 0)throw new I("WASM query compiler was unexpectedly `undefined`",t);return co===void 0&&(co=(async()=>{let i=await n.getRuntime(),o=await n.getQueryCompilerWasmModule();if(o==null)throw new I("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let s={"./query_compiler_bg.js":i},a=new WebAssembly.Instance(o,s),l=a.exports.__wbindgen_start;return i.__wbg_set_wasm(a.exports),l(),i.QueryCompiler})()),await co}};var au="P2038",$n=L("prisma:client:clientEngine"),uu=globalThis;uu.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new ne(e,Ln)}};var St=class{name="ClientEngine";queryCompiler;instantiateQueryCompilerPromise;QueryCompilerConstructor;queryCompilerLoader;adapterPromise;transactionManagerPromise;config;provider;datamodel;logEmitter;logQueries;logLevel;lastStartedQuery;tracingHelper;#e;constructor(t,r){if(!t.previewFeatures?.includes("driverAdapters"))throw new I("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,au);if(t.adapter)this.adapterPromise=t.adapter.connect(),this.provider=t.adapter.provider,$n("Using driver adapter: %O",t.adapter);else throw new I("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,au);this.queryCompilerLoader=r??su,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#e=n=>{this.logEmitter.emit("query",{...n,params:JSON.stringify(n.params),target:"ClientEngine"})}),this.transactionManagerPromise=this.adapterPromise.then(n=>new xr({driverAdapter:n,transactionOptions:this.config.transactionOptions,tracingHelper:this.tracingHelper})),this.instantiateQueryCompilerPromise=this.instantiateQueryCompiler()}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async instantiateQueryCompiler(){if(!this.queryCompiler){this.QueryCompilerConstructor||(this.QueryCompilerConstructor=await this.queryCompilerLoader.loadQueryCompiler(this.config));try{this.#r(()=>{this.queryCompiler=new this.QueryCompilerConstructor({datamodel:this.datamodel,provider:this.provider,connectionInfo:{}})})}catch(t){throw this.#n(t)}}}#n(t){if(t instanceof ne)return t;try{let r=JSON.parse(t.message);return new I(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#t(t){if(t instanceof I)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new ne(lu(this,t.message),this.config.clientVersion);if(t instanceof ee)return new Q(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let r=JSON.parse(t);return new Y(`${r.message}
${r.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#i(t){return t instanceof ne?t:typeof t.message=="string"&&typeof t.code=="string"?new Q(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#r(t){let r=uu.PRISMA_WASM_PANIC_REGISTRY.set_message,n;global.PRISMA_WASM_PANIC_REGISTRY.set_message=i=>{n=i};try{return t()}finally{if(global.PRISMA_WASM_PANIC_REGISTRY.set_message=r,n)throw new ne(lu(this,n),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.tracingHelper.runInChildSpan("connect",()=>this.ensureStarted())}async stop(){await this.tracingHelper.runInChildSpan("disconnect",async()=>{await this.instantiateQueryCompilerPromise,await(await this.transactionManagerPromise)?.cancelAllTransactions(),await(await this.adapterPromise).dispose()})}async ensureStarted(){let t=await this.adapterPromise,r=await this.transactionManagerPromise;return await this.instantiateQueryCompilerPromise,[t,r]}version(){return"unknown"}async transaction(t,r,n){let i,o=await this.transactionManagerPromise;try{if(t==="start"){let s=n;i=await o.startTransaction(s)}else if(t==="commit"){let s=n;await o.commitTransaction(s.id)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s.id)}else pe(t,"Invalid transaction action.")}catch(s){throw this.#t(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{traceparent:r,interactiveTransaction:n}){$n("sending request");let i=JSON.stringify(t);this.lastStartedQuery=i;let[o,s]=await this.ensureStarted().catch(l=>{throw this.#t(l)}),a;try{a=this.#r(()=>this.queryCompiler.compile(i))}catch(l){throw this.#i(l)}try{let l=JSON.parse(a);$n("query plan created",a);let u=n?s.getTransaction(n,"query"):o,c=n?{enabled:!1}:{enabled:!0,manager:s},p={},m=await Ct.forSql({transactionManager:c,placeholderValues:p,onQuery:this.#e,tracingHelper:this.tracingHelper}).run(l,u);return $n("query plan executed"),{data:{[t.action]:m}}}catch(l){throw this.#t(l)}}async requestBatch(t,{transaction:r,traceparent:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(Et(t,r));this.lastStartedQuery=o;let[,s]=await this.ensureStarted().catch(l=>{throw this.#t(l)}),a;try{a=this.queryCompiler.compileBatch(o)}catch(l){throw this.#i(l)}try{let l;if(r?.kind==="itx")l=r.options;else{let m=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;l=await this.transaction("start",{},m)}let u={},c=Ct.forSql({transactionManager:{enabled:!1},placeholderValues:u,onQuery:this.#e,tracingHelper:this.tracingHelper}),p=s.getTransaction(l,"batch query"),d=[];switch(a.type){case"multi":{d=await Promise.all(a.plans.map(async(m,g)=>{let h=await c.run(m,p);return{data:{[t[g].action]:h}}}));break}case"compacted":{if(!t.every(g=>g.action===i))throw new Error("All queries in a batch must have the same action");let m=await c.run(a.plan,p);d=this.#o(m,a,i);break}}return r?.kind!=="itx"&&await this.transaction("commit",{},l),d}catch(l){throw this.#t(l)}}metrics(t){throw new Error("Method not implemented.")}#o(t,r,n){let i=t.map(s=>r.keys.reduce((a,l)=>(a[l]=s[l],a),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let a=i.findIndex(l=>Zf(l,s));if(a===-1)return r.expectNonEmpty?new Q("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let l=Object.entries(t[a]).filter(([u])=>o.has(u));return{data:{[n]:Object.fromEntries(l)}}}})}};function lu(e,t){return Ua({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:e.lastStartedQuery})}function Zf(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>cu(e[o],t[o]))}function cu(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>cu(e[r],t[r]))}function Rt({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new I(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new I("error: Missing URL environment variable, value, or override.",n);return i}var Vn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var oe=class extends Vn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};function A(e,t){return{...e,isRetryable:t}}var kt=class extends oe{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",A(t,!0))}};x(kt,"ForcedRetryError");var Ze=class extends oe{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,A(r,!1))}};x(Ze,"InvalidDatasourceError");var Xe=class extends oe{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,A(r,!1))}};x(Xe,"NotImplementedYetError");var M=class extends oe{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var et=class extends M{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",A(t,!0))}};x(et,"SchemaMissingError");var po="This request could not be understood by the server",Pr=class extends M{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||po,A(t,!1)),n&&(this.code=n)}};x(Pr,"BadRequestError");var vr=class extends M{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",A(t,!0)),this.logs=r}};x(vr,"HealthcheckTimeoutError");var Tr=class extends M{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,A(t,!0)),this.logs=n}};x(Tr,"EngineStartupError");var Cr=class extends M{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",A(t,!1))}};x(Cr,"EngineVersionNotSupportedError");var mo="Request timed out",Ar=class extends M{name="GatewayTimeoutError";code="P5009";constructor(t,r=mo){super(r,A(t,!1))}};x(Ar,"GatewayTimeoutError");var Xf="Interactive transaction error",Sr=class extends M{name="InteractiveTransactionError";code="P5015";constructor(t,r=Xf){super(r,A(t,!1))}};x(Sr,"InteractiveTransactionError");var eg="Request parameters are invalid",Rr=class extends M{name="InvalidRequestError";code="P5011";constructor(t,r=eg){super(r,A(t,!1))}};x(Rr,"InvalidRequestError");var fo="Requested resource does not exist",kr=class extends M{name="NotFoundError";code="P5003";constructor(t,r=fo){super(r,A(t,!1))}};x(kr,"NotFoundError");var go="Unknown server error",It=class extends M{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||go,A(t,!0)),this.logs=n}};x(It,"ServerError");var ho="Unauthorized, check your connection string",Ir=class extends M{name="UnauthorizedError";code="P5007";constructor(t,r=ho){super(r,A(t,!1))}};x(Ir,"UnauthorizedError");var yo="Usage exceeded, retry again later",Or=class extends M{name="UsageExceededError";code="P5008";constructor(t,r=yo){super(r,A(t,!0))}};x(Or,"UsageExceededError");async function tg(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Dr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await tg(e);if(n.type==="QueryEngineError")throw new Q(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new It(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new et(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Cr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Tr(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new I(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new vr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Sr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Rr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Ir(r,Ot(ho,n));if(e.status===404)return new kr(r,Ot(fo,n));if(e.status===429)throw new Or(r,Ot(yo,n));if(e.status===504)throw new Ar(r,Ot(mo,n));if(e.status>=500)throw new It(r,Ot(go,n));if(e.status>=400)throw new Pr(r,Ot(po,n))}function Ot(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function pu(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var Ie="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function du(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,l,u,c;for(let p=0;p<o;p=p+3)c=t[p]<<16|t[p+1]<<8|t[p+2],s=(c&16515072)>>18,a=(c&258048)>>12,l=(c&4032)>>6,u=c&63,r+=Ie[s]+Ie[a]+Ie[l]+Ie[u];return i==1?(c=t[o],s=(c&252)>>2,a=(c&3)<<4,r+=Ie[s]+Ie[a]+"=="):i==2&&(c=t[o]<<8|t[o+1],s=(c&64512)>>10,a=(c&1008)>>4,l=(c&15)<<2,r+=Ie[s]+Ie[a]+Ie[l]+"="),r}function mu(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new I("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}function rg(e){return e[0]*1e3+e[1]/1e6}function wo(e){return new Date(rg(e))}var fu={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var _r=class extends oe{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,A(r,!0))}};x(_r,"RequestError");async function tt(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new _r(a,{clientVersion:n,cause:s})}}var ig=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,gu=L("prisma:client:dataproxyEngine");async function og(e,t){let r=fu["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&ig.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,l,u]=s.split("."),c=sg(`<=${a}.${l}.${u}`),p=await tt(c,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let d=await p.text();gu("length of body fetched from unpkg.com",d.length);let m;try{m=JSON.parse(d)}catch(g){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),g}return m.version}throw new Xe("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function hu(e,t){let r=await og(e,t);return gu("version",r),r}function sg(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var yu=3,Nr=L("prisma:client:dataproxyEngine"),Eo=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},Mr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){mu(t),this.config=t,this.env={...t.env,...typeof process<"u"?process.env:{}},this.inlineSchema=du(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.headerBuilder=new Eo({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=Yn(r)?"http":"https",this.remoteClientVersion=await hu(this.host,this.config),Nr("host",this.host),Nr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Nr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:wo(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:wo(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await tt(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Nr("schema response status",r.status);let n=await Dr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Et(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(l=>(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l?this.convertProtocolErrorsToClientError(l.errors):l))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await tt(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,interactiveTransaction:i}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||Nr("graphql response status",a.status),await this.handleError(await Dr(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await tt(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Dr(l,this.clientVersion));let u=await l.json(),{extensions:c}=u;c&&this.propagateResponseExtensions(c);let p=u.id,d=u["data-proxy"].endpoint;return{id:p,payload:{endpoint:d}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await tt(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Dr(a,this.clientVersion));let l=await a.json(),{extensions:u}=l;u&&this.propagateResponseExtensions(u);return}}})}getURLAndAPIKey(){let t={clientVersion:this.clientVersion},r=Object.keys(this.inlineDatasources)[0],n=Rt({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new Ze(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==$r)throw new Ze(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new Ze(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);return{apiKey:a,url:i}}metrics(){throw new Xe("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof oe)||!i.isRetryable)throw i;if(r>=yu)throw i instanceof kt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${yu} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await pu(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof et)throw await this.uploadSchema(),new kt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?wn(t[0],this.config.clientVersion,this.config.activeProvider):new Y(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function wu({copyEngine:e=!0},t){let r;try{r=Rt({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let n=!!(r?.startsWith("prisma://")||Vr(r));e&&n&&Ur("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let i=it(t.generator),o=n||!e,s=!!t.adapter,a=i==="library",l=i==="binary",u=i==="client";if(o&&s||s&&!1){let c;throw e?r?.startsWith("prisma://")?c=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:c=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:c=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new Z(c.join(`
`),{clientVersion:t.clientVersion})}return o?new Mr(t):u?new St(t):new St(t)}function qn({generator:e}){return e?.previewFeatures??[]}var Eu=e=>({command:e});var bu=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function Dt(e){try{return xu(e,"fast")}catch{return xu(e,"slow")}}function xu(e,t){return JSON.stringify(e.map(r=>vu(r,t)))}function vu(e,t){if(Array.isArray(e))return e.map(r=>vu(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(lt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(ae.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(Buffer.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(ag(e))return{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:Buffer.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Tu(e):e}function ag(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Tu(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Pu);let t={};for(let r of Object.keys(e))t[r]=Pu(e[r]);return t}function Pu(e){return typeof e=="bigint"?e.toString():Tu(e)}var lg=/^(\s*alter\s)/i,Cu=L("prisma:client");function bo(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&lg.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var xo=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(gn(r))n=r.sql,i={values:Dt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:Dt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:Dt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:Dt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=bu(r),i={values:Dt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Cu(`prisma.${e}(${n}, ${i.values})`):Cu(`prisma.${e}(${n})`),{query:n,parameters:i}},Au={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new ue(t,r)}},Su={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function Po(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=Ru(r(s)):Ru(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function Ru(e){return typeof e.then=="function"?e:Promise.resolve(e)}var ug=Kn.split(".")[0],cg={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},vo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${ug}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??cg}};function ku(){return new vo}function Iu(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function Ou(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var jn=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};var _u=ge(Xn());function Un(e){return typeof e.batchRequestIdx=="number"}function Du(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(To(e.query.arguments)),t.push(To(e.query.selection)),t.join("")}function To(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${To(n)})`:r}).join(" ")})`}var pg={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Co(e){return pg[e]}var Bn=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function rt(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new ae(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>rt("bigint",r));case"bytes-array":return t.map(r=>rt("bytes",r));case"decimal-array":return t.map(r=>rt("decimal",r));case"datetime-array":return t.map(r=>rt("datetime",r));case"date-array":return t.map(r=>rt("date",r));case"time-array":return t.map(r=>rt("time",r));default:return t}}function Ao(e){let t=[],r=dg(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=rt(e.types[s],i[s]);t.push(o)}return t}function dg(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var mg=L("prisma:client:request_handler"),Qn=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new Bn({batchLoader:Oa(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),u=n.some(p=>Co(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:fg(o),containsWrite:u,customDataProxyFetch:i})).map((p,d)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[d],p)}catch(m){return m}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Nu(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Co(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Du(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return process.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(mg(t),gg(t,i))throw t;if(t instanceof Q&&hg(t)){let u=Mu(t.meta);pn({args:o,errors:[u],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let l=t.message;if(n&&(l=tn({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:l})),l=this.sanitizeMessage(l),t.code){let u=s?{modelName:s,...t.meta}:t.meta;throw new Q(l,{code:t.code,clientVersion:this.client._clientVersion,meta:u,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new ne(l,this.client._clientVersion);if(t instanceof Y)throw new Y(l,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof I)throw new I(l,this.client._clientVersion);if(t instanceof ne)throw new ne(l,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,_u.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(u=>u!=="select"&&u!=="include"),a=Ri(o,s),l=i==="queryRaw"?Ao(a):Wt(a);return n?n(l):l}get[Symbol.toStringTag](){return"RequestHandler"}};function fg(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Nu(e)};pe(e,"Unknown transaction kind")}}function Nu(e){return{id:e.id,payload:e.payload}}function gg(e,t){return Un(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function hg(e){return e.code==="P2009"||e.code==="P2012"}function Mu(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Mu)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}var Fu=Ln;var ju=ge(fi());var O=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};x(O,"PrismaClientConstructorValidationError");var Lu=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],$u=["pretty","colorless","minimal"],Vu=["info","query","warn","error"],yg={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new O(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=_t(r,t)||` Available datasources: ${t.join(", ")}`;throw new O(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new O(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new O(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new O(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&it(t.generator)==="client")throw new O('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new O('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!qn(t).includes("driverAdapters"))throw new O('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(it(t.generator)==="binary")throw new O('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new O(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new O(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!$u.includes(e)){let t=_t(e,$u);throw new O(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new O(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Vu.includes(r)){let n=_t(r,Vu);throw new O(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=_t(i,o);throw new O(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new O(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new O(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new O(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new O('"omit" option is expected to be an object.');if(e===null)throw new O('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Eg(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let l=o.fields.find(u=>u.name===s);if(!l){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(l.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new O(bg(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new O(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=_t(r,t);throw new O(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Uu(e,t){for(let[r,n]of Object.entries(e)){if(!Lu.includes(r)){let i=_t(r,Lu);throw new O(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}yg[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new O('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function _t(e,t){if(t.length===0||typeof e!="string")return"";let r=wg(e,t);return r?` Did you mean "${r}"?`:""}function wg(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,ju.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Eg(e,t){return qu(t.models,e)??qu(t.types,e)}function qu(e,t){let r=Object.keys(e).find(n=>Me(n)===t);if(r)return e[r]}function bg(e,t){let r=ht(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=cn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}function Bu(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=u=>{o||(o=!0,r(u))};for(let u=0;u<e.length;u++)e[u].then(c=>{n[u]=c,a()},c=>{if(!Un(c)){l(c);return}c.batchRequestIdx===u?l(c):(i||(i=c),a())})})}var je=L("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var Tg={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Cg=Symbol.for("prisma.client.transaction.id"),Ag={id:0,nextId(){return++this.id}};function Sg(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new jn;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=Po();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Fa(e),n&&Uu(n,e);let i=new Pg().on("error",()=>{});this._extensions=yt.empty(),this._previewFeatures=qn(e),this._clientVersion=e.clientVersion??Fu,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=ku();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&So.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&So.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let l=e.activeProvider==="postgresql"?"postgres":e.activeProvider;if(s.provider!==l)throw new I(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new I("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=!s&&o&&Ht(o,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},u=l.__internal??{},c=u.debug===!0;c&&L.enable("prisma:client");let p=So.resolve(e.dirname,e.relativePath);vg.existsSync(p)||(p=e.dirname),je("dirname",e.dirname),je("relativePath",e.relativePath),je("cwd",p);let d=u.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:c,allowTriggerPanic:d.allowTriggerPanic,prismaPath:d.binaryPath??void 0,engineEndpoint:d.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&Ou(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(m=>typeof m=="string"?m==="query":m.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:La(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Rt,getBatchRequestPayload:Et,prismaGraphQLToJSError:wn,PrismaClientUnknownRequestError:Y,PrismaClientInitializationError:I,PrismaClientKnownRequestError:Q,debug:L("prisma:client:accelerateEngine"),engineVersion:Hu.version,clientVersion:e.clientVersion}},je("clientVersion",e.clientVersion),this._engine=wu(e,this._engineConfig),this._requestHandler=new Qn(this,i),l.log)for(let m of l.log){let g=typeof m=="string"?m:m.emit==="stdout"?m.level:null;g&&this.$on(g,h=>{Ut.log(`${Ut.tags[g]??""}`,h.message||h.query)})}}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=ur(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{Uo()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:xo({clientMethod:i,activeProvider:a}),callsite:Le(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Qu(n,i);return bo(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new Z("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(bo(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new Z(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Eu,callsite:Le(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:xo({clientMethod:i,activeProvider:a}),callsite:Le(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Qu(n,i));throw new Z("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new Z("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=Ag.nextId(),s=Iu(n.length),a=n.map((l,u)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let c=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:u,isolationLevel:c,lock:s};return l.requestTransaction?.(p)??l});return Bu(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let u={kind:"itx",...a};l=await n(this._createItxClient(u)),await this._engine.transaction("commit",o,a)}catch(u){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),u}return l}_createItxClient(n){return fe(ur(fe(xa(this),[X("_appliedParent",()=>this._appliedParent._createItxClient(n)),X("_createPrismaPromise",()=>Po(n)),X(Cg,()=>n.id)])),[wt(Aa)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??Tg,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,l=async u=>{let c=this._middlewares.get(++a);if(c)return this._tracingHelper.runInChildSpan(s.middleware,R=>c(u,T=>(R?.end(),l(T))));let{runInTransaction:p,args:d,...m}=u,g={...n,...m};d&&(g.args=i.middlewareArgsToRequestArgs(d)),n.transaction!==void 0&&p===!1&&delete g.transaction;let h=await Ia(this,g);return g.model?Ca({result:h,modelName:g.model,args:g.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):h};return this._tracingHelper.runInChildSpan(s.operation,()=>new xg("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:u,transaction:c,unpacker:p,otelParentCtx:d,customDataProxyFetch:m}){try{n=u?u(n):n;let g={name:"serialize"},h=this._tracingHelper.runInChildSpan(g,()=>Pi({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return L.enabled("prisma:client")&&(je("Prisma Client call:"),je(`prisma.${i}(${ca(n)})`),je("Generated request:"),je(JSON.stringify(h,null,2)+`
`)),c?.kind==="batch"&&await c.lock,this._requestHandler.request({protocolQuery:h,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:c,unpacker:p,otelParentCtx:d,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:m})}catch(g){throw g.clientVersion=this._clientVersion,g}}$metrics=new or(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=Pa}return t}function Qu(e,t){return Rg(e)?[new ue(e,t),Au]:[e,Su]}function Rg(e){return Array.isArray(e)&&Array.isArray(e.raw)}var kg=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Ig(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!kg.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}function Og(e){Ht(e,{conflictCheck:"warn"})}export{Zr as DMMF,L as Debug,ae as Decimal,Oo as Extensions,or as MetricsClient,I as PrismaClientInitializationError,Q as PrismaClientKnownRequestError,ne as PrismaClientRustPanicError,Y as PrismaClientUnknownRequestError,Z as PrismaClientValidationError,_o as Public,ue as Sql,Sd as createParam,Ld as defineDmmfProperty,Wt as deserializeJsonResponse,Ao as deserializeRawResult,Vp as dmmfToRuntimeDataModel,Ud as empty,Sg as getPrismaClient,Di as getRuntime,jd as join,Ig as makeStrictEnum,Vd as makeTypedQueryFactory,yi as objectEnumValues,sa as raw,Pi as serializeJsonQuery,bi as skip,aa as sqltag,Og as warnEnvConflicts,Ur as warnOnce};
/*! Bundled license information:

@noble/hashes/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=client.mjs.map
