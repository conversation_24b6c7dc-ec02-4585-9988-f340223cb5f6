<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for backend/models</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1><a href="../../index.html">All files</a> backend/models</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/719</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/637</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/55</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/715</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th data-col="statements" data-type="number" data-fmt="pct" class="pct">
                Statements
              </th>
              <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
              <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
              <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
              <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file low" data-value="foalModel.js">
                <a href="foalModel.js.html">foalModel.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="110" class="abs low">0/110</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="80" class="abs low">0/80</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="11" class="abs low">0/11</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="107" class="abs low">0/107</td>
            </tr>

            <tr>
              <td class="file low" data-value="horseModel.js">
                <a href="horseModel.js.html">horseModel.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="274" class="abs low">0/274</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="321" class="abs low">0/321</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="19" class="abs low">0/19</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="273" class="abs low">0/273</td>
            </tr>

            <tr>
              <td class="file low" data-value="resultModel.js">
                <a href="resultModel.js.html">resultModel.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="53" class="abs low">0/53</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="48" class="abs low">0/48</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="53" class="abs low">0/53</td>
            </tr>

            <tr>
              <td class="file low" data-value="trainingModel.js">
                <a href="trainingModel.js.html">trainingModel.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="58" class="abs low">0/58</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="30" class="abs low">0/30</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="58" class="abs low">0/58</td>
            </tr>

            <tr>
              <td class="file low" data-value="userModel.js">
                <a href="userModel.js.html">userModel.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="161" class="abs low">0/161</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="124" class="abs low">0/124</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="13" class="abs low">0/13</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="161" class="abs low">0/161</td>
            </tr>

            <tr>
              <td class="file low" data-value="xpLogModel.js">
                <a href="xpLogModel.js.html">xpLogModel.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="63" class="abs low">0/63</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="34" class="abs low">0/34</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="63" class="abs low">0/63</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="../../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../../sorter.js"></script>
    <script src="../../block-navigation.js"></script>
  </body>
</html>
