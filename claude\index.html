<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Equoria Mobile Wireframes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            line-height: 1.4;
        }
        
        .wireframe-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .phone-frame {
            width: 320px;
            height: 640px;
            background: white;
            border: 3px solid #333;
            border-radius: 25px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .screen-header {
            background: #2C5530;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: relative;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-bottom: 8px;
            opacity: 0.8;
        }
        
        .screen-content {
            padding: 15px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 10px;
            color: #666;
            text-decoration: none;
        }
        
        .nav-item.active {
            color: #2C5530;
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            background: #ddd;
            border-radius: 50%;
            margin-bottom: 4px;
            position: relative;
        }
        
        .nav-item.active .nav-icon {
            background: #2C5530;
        }
        
        /* Horse icon representation */
        .nav-icon.horse::after {
            content: "🐎";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
        }
        
        .hero-card {
            background: linear-gradient(135deg, #2C5530, #4A7C59);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .hero-horse {
            width: 80px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #DAA520;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2C5530;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .horse-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .horse-card {
            background: white;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border: 2px solid #f0f0f0;
        }
        
        .horse-avatar {
            width: 100%;
            height: 80px;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: white;
        }
        
        .horse-name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 4px;
            color: #2C5530;
        }
        
        .horse-breed {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .progress-bar {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 4px;
        }
        
        .progress-fill {
            height: 100%;
            background: #DAA520;
            border-radius: 3px;
        }
        
        .filter-chips {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .chip {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            font-size: 12px;
            color: #495057;
        }
        
        .chip.active {
            background: #2C5530;
            color: white;
            border-color: #2C5530;
        }
        
        .action-button {
            width: 100%;
            padding: 8px;
            background: #DAA520;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 8px;
        }
        
        .horse-profile-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-bottom: 2px solid transparent;
        }
        
        .tab.active {
            color: #2C5530;
            border-bottom-color: #2C5530;
        }
        
        .radar-chart {
            width: 150px;
            height: 150px;
            background: #f8f9fa;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 12px;
        }
        
        .training-options {
            display: grid;
            gap: 12px;
        }
        
        .training-card {
            background: white;
            padding: 15px;
            border-radius: 12px;
            border-left: 4px solid #2C5530;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .training-icon {
            width: 40px;
            height: 40px;
            background: #2C5530;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .training-info {
            flex: 1;
        }
        
        .training-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .training-time {
            font-size: 12px;
            color: #666;
        }
        
        .competition-card {
            background: white;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 12px;
            border: 2px solid #DAA520;
        }
        
        .competition-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .competition-name {
            font-weight: bold;
            color: #2C5530;
        }
        
        .prize-amount {
            color: #DAA520;
            font-weight: bold;
        }
        
        .requirements {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
        }
        
        .requirement-item {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .wireframe-title {
            text-align: center;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #2C5530;
        }
        
        .screen-description {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="wireframe-container">
        
        <!-- Dashboard Screen -->
        <div class="phone-frame">
            <div class="wireframe-title">Dashboard</div>
            <div class="screen-description">Main hub showing player status, featured horse, and quick actions</div>
            <div class="screen-header">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div>Equoria</div>
            </div>
            <div class="screen-content">
                <div class="hero-card">
                    <div class="hero-horse">🐎</div>
                    <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">Thunder's Dawn</div>
                    <div style="font-size: 12px; opacity: 0.8;">Arabian • Level 12</div>
                    <div style="font-size: 12px; margin-top: 8px;">Ready for training</div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Horses</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div class="stat-label">Competitions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">$2,450</div>
                        <div class="stat-label">Earnings</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Training Slots</div>
                    </div>
                </div>
                
                <div style="margin-bottom: 10px; font-weight: bold; color: #2C5530;">Recent Activity</div>
                <div style="background: white; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                    <div style="font-size: 14px; margin-bottom: 4px;">🏆 Midnight Storm placed 2nd in Spring Derby</div>
                    <div style="font-size: 12px; color: #666;">2 hours ago • +$500</div>
                </div>
                <div style="background: white; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                    <div style="font-size: 14px; margin-bottom: 4px;">✨ Thunder's Dawn completed Endurance Training</div>
                    <div style="font-size: 12px; color: #666;">4 hours ago • +25 XP</div>
                </div>
            </div>
            
            <div class="bottom-nav">
                <a href="#" class="nav-item active">
                    <div class="nav-icon horse"></div>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Stable</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Compete</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>World</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>More</span>
                </a>
            </div>
        </div>
        
        <!-- Stable Screen -->
        <div class="phone-frame">
            <div class="wireframe-title">Stable Management</div>
            <div class="screen-description">Grid view of all horses with filtering and quick actions</div>
            <div class="screen-header">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div>My Stable (8 Horses)</div>
            </div>
            <div class="screen-content">
                <div class="filter-chips">
                    <div class="chip active">All</div>
                    <div class="chip">Arabian</div>
                    <div class="chip">Thoroughbred</div>
                    <div class="chip">Ready</div>
                </div>
                
                <div class="horse-grid">
                    <div class="horse-card">
                        <div class="horse-avatar">🐎</div>
                        <div class="horse-name">Thunder's Dawn</div>
                        <div class="horse-breed">Arabian • Lvl 12</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 80%;"></div>
                        </div>
                        <div style="font-size: 10px; color: #666; margin-bottom: 8px;">Energy: 80%</div>
                        <button class="action-button">Train</button>
                    </div>
                    
                    <div class="horse-card">
                        <div class="horse-avatar" style="background: linear-gradient(45deg, #654321, #8B4513);">🐴</div>
                        <div class="horse-name">Midnight Storm</div>
                        <div class="horse-breed">Thoroughbred • Lvl 15</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%;"></div>
                        </div>
                        <div style="font-size: 10px; color: #666; margin-bottom: 8px;">Energy: 60%</div>
                        <button class="action-button">Compete</button>
                    </div>
                    
                    <div class="horse-card">
                        <div class="horse-avatar" style="background: linear-gradient(45deg, #DEB887, #F4A460);">🐎</div>
                        <div class="horse-name">Golden Whisper</div>
                        <div class="horse-breed">Quarter Horse • Lvl 8</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 40%;"></div>
                        </div>
                        <div style="font-size: 10px; color: #666; margin-bottom: 8px;">Energy: 40%</div>
                        <button class="action-button" style="background: #ccc;">Resting (2h)</button>
                    </div>
                    
                    <div class="horse-card">
                        <div class="horse-avatar" style="background: linear-gradient(45deg, #2F4F4F, #696969);">🐴</div>
                        <div class="horse-name">Silver Moon</div>
                        <div class="horse-breed">Andalusian • Lvl 10</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%;"></div>
                        </div>
                        <div style="font-size: 10px; color: #666; margin-bottom: 8px;">Energy: 90%</div>
                        <button class="action-button">Train</button>
                    </div>
                </div>
                
                <div style="margin-top: 20px; text-align: center;">
                    <button style="width: 60px; height: 60px; border-radius: 50%; background: #DAA520; border: none; color: white; font-size: 24px;">+</button>
                    <div style="font-size: 12px; color: #666; margin-top: 8px;">Add New Horse</div>
                </div>
            </div>
            
            <div class="bottom-nav">
                <a href="#" class="nav-item">
                    <div class="nav-icon horse"></div>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item active">
                    <div class="nav-icon"></div>
                    <span>Stable</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Compete</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>World</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>More</span>
                </a>
            </div>
        </div>
        
        <!-- Horse Profile Screen -->
        <div class="phone-frame">
            <div class="wireframe-title">Horse Profile</div>
            <div class="screen-description">Detailed view of individual horse with stats, training, and history</div>
            <div class="screen-header">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div>← Thunder's Dawn</div>
            </div>
            <div class="screen-content">
                <div class="horse-profile-header">
                    <div class="profile-avatar">🐎</div>
                    <div style="font-size: 20px; font-weight: bold; color: #2C5530;">Thunder's Dawn</div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 5px;">Arabian Mare • Age 4</div>
                    <div style="font-size: 12px; color: #DAA520;">Level 12 • 2,450 XP</div>
                </div>
                
                <div class="tabs">
                    <div class="tab active">Stats</div>
                    <div class="tab">Training</div>
                    <div class="tab">History</div>
                    <div class="tab">Breeding</div>
                </div>
                
                <div class="radar-chart">
                    <div>Stats Radar Chart<br/>Speed: 85<br/>Stamina: 78<br/>Jumping: 62<br/>Dressage: 90</div>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="font-weight: bold; margin-bottom: 8px;">Attributes</div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="font-size: 12px;">Speed</span>
                        <span style="font-size: 12px; color: #DAA520;">85/100</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%;"></div>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                        <span style="font-size: 12px;">Stamina</span>
                        <span style="font-size: 12px; color: #DAA520;">78/100</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                        <span style="font-size: 12px;">Jumping</span>
                        <span style="font-size: 12px; color: #DAA520;">62/100</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 62%;"></div>
                    </div>
                </div>
                
                <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #2C5530;">
                    <div style="font-weight: bold; margin-bottom: 8px;">Temperament</div>
                    <div style="font-size: 12px; color: #666;">Spirited • Intelligent • Loyal</div>
                </div>
            </div>
            
            <div class="bottom-nav">
                <button style="flex: 1; background: #DAA520; color: white; border: none; margin: 10px; padding: 12px; border-radius: 8px; font-weight: bold;">Start Training</button>
                <button style="flex: 1; background: #2C5530; color: white; border: none; margin: 10px; padding: 12px; border-radius: 8px; font-weight: bold;">Enter Competition</button>
            </div>
        </div>
        
        <!-- Training Screen -->
        <div class="phone-frame">
            <div class="wireframe-title">Training Center</div>
            <div class="screen-description">Available training activities with timers and requirements</div>
            <div class="screen-header">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div>← Training Center</div>
            </div>
            <div class="screen-content">
                <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 15px; text-align: center;">
                    <div style="font-size: 16px; font-weight: bold; color: #2C5530;">Thunder's Dawn</div>
                    <div style="font-size: 12px; color: #666;">Energy: 80% • Ready to train</div>
                </div>
                
                <div class="training-options">
                    <div class="training-card">
                        <div class="training-icon">🏃</div>
                        <div class="training-info">
                            <div class="training-name">Speed Training</div>
                            <div class="training-time">Duration: 2 hours • +Speed</div>
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">Requires: Energy 60%+</div>
                        </div>
                        <button style="background: #DAA520; color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 12px;">Start</button>
                    </div>
                    
                    <div class="training-card">
                        <div class="training-icon">💪</div>
                        <div class="training-info">
                            <div class="training-name">Endurance Training</div>
                            <div class="training-time">Duration: 3 hours • +Stamina</div>
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">Requires: Energy 70%+</div>
                        </div>
                        <button style="background: #DAA520; color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 12px;">Start</button>
                    </div>
                    
                    <div class="training-card">
                        <div class="training-icon">🎯</div>
                        <div class="training-info">
                            <div class="training-name">Jumping Practice</div>
                            <div class="training-time">Duration: 1.5 hours • +Jumping</div>
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">Requires: Level 10+</div>
                        </div>
                        <button style="background: #DAA520; color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 12px;">Start</button>
                    </div>
                    
                    <div class="training-card" style="opacity: 0.6;">
                        <div class="training-icon">🎭</div>
                        <div class="training-info">
                            <div class="training-name">Dressage Training</div>
                            <div class="training-time">Duration: 2.5 hours • +Dressage</div>
                            <div style="font-size: 11px; color: #DC143C; margin-top: 4px;">Requires: Level 15+</div>
                        </div>
                        <button style="background: #ccc; color: #666; border: none; padding: 8px 12px; border-radius: 6px; font-size: 12px;" disabled>Locked</button>
                    </div>
                </div>
                
                <div style="margin-top: 20px; background: white; padding: 15px; border-radius: 8px;">
                    <div style="font-weight: bold; margin-bottom: 10px; color: #2C5530;">Active Training Sessions</div>
                    <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-size: 14px; font-weight: bold;">Golden Whisper</div>
                                <div style="font-size: 12px; color: #666;">Speed Training</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 14px; font-weight: bold; color: #DAA520;">1h 23m</div>
                                <div style="font-size: 12px; color: #666;">remaining</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bottom-nav">
                <a href="#" class="nav-item">
                    <div class="nav-icon horse"></div>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item active">
                    <div class="nav-icon"></div>
                    <span>Stable</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>Compete</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>World</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon"></div>
                    <span>More</span>
                </a>
            </div>
        </div>
        
        <!-- Competition Screen -->
        <div class="phone-frame">
            <div class="wireframe-title">Competitions</div>
            <div class="screen-description">Available competitions with entry requirements and prizes</div>
            <div class="screen-header">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚡ 100%</span>
                </div>
                <div>Competitions</div>
            </div>
            <div class="screen-content">
                <div class="filter-chips">
                    <div class="chip active">All</div>
                    <div class="chip">Speed</div>
                </div>
            </div>
        </div>
        <div>
            <a href="/claudestyleguide.html">Style Guide</a>
        </div>
    </div>
</body>
</html>