/**
 * Conformation Show Service
 * Handles conformation-based competitions where grooms act as handlers
 * Scoring: 60-70% conformation + 15-25% groom handling + 10-15% bond/temperament
 */

import prisma from '../db/index.mjs';
import logger from '../utils/logger.mjs';
import { CONFORMATION_DISCIPLINES } from '../constants/schema.mjs';

// Conformation show configuration
export const CONFORMATION_SHOW_CONFIG = {
  // Scoring weights
  CONFORMATION_WEIGHT: 0.65,  // 65% - horse's physical conformation
  HANDLER_WEIGHT: 0.20,       // 20% - groom's handling skill
  BOND_WEIGHT: 0.15,          // 15% - bond score and temperament synergy

  // Conformation region weights (must sum to 1.0)
  CONFORMATION_REGIONS: {
    head: 0.15,        // 15% - Head and facial features
    neck: 0.12,        // 12% - Neck set and length
    shoulders: 0.13,   // 13% - Shoulder angle and slope
    back: 0.15,        // 15% - Back length and strength
    legs: 0.18,        // 18% - Leg conformation and soundness
    hooves: 0.10,      // 10% - Hoof quality and shape
    topline: 0.12,     // 12% - Topline and muscle development
    hindquarters: 0.05 // 5% - Hip and hindquarter development
  },

  // Handler skill bonuses by level
  HANDLER_SKILL_BONUSES: {
    novice: { base: 0.70, max: 0.85 },      // 70-85% effectiveness
    intermediate: { base: 0.80, max: 0.95 }, // 80-95% effectiveness
    expert: { base: 0.90, max: 1.05 },      // 90-105% effectiveness
    master: { base: 1.00, max: 1.15 }       // 100-115% effectiveness
  },

  // Temperament-handler personality synergy
  TEMPERAMENT_SYNERGY: {
    calm: {
      beneficial: ['gentle', 'patient', 'calm'],
      detrimental: ['energetic', 'strict'],
      bonus: 0.10
    },
    spirited: {
      beneficial: ['energetic', 'confident', 'strict'],
      detrimental: ['gentle', 'patient'],
      bonus: 0.12
    },
    nervous: {
      beneficial: ['gentle', 'patient', 'calm'],
      detrimental: ['energetic', 'strict', 'confident'],
      bonus: 0.15
    },
    aggressive: {
      beneficial: ['strict', 'confident'],
      detrimental: ['gentle', 'patient'],
      bonus: 0.08
    }
  }
};

/**
 * Check if a discipline is a conformation show
 * @param {string} discipline - Discipline name
 * @returns {boolean} True if conformation show
 */
export function isConformationShow(discipline) {
  return Object.values(CONFORMATION_DISCIPLINES).includes(discipline);
}

/**
 * Calculate conformation score from horse's conformation regions
 * @param {Object} conformationScores - Horse's conformation scores object
 * @returns {number} Weighted conformation score (0-100)
 */
export function calculateConformationScore(conformationScores) {
  try {
    if (!conformationScores || typeof conformationScores !== 'object') {
      logger.warn('[conformationShowService] Invalid conformation scores, using default');
      return 20; // Default score
    }

    let totalScore = 0;
    const regions = CONFORMATION_SHOW_CONFIG.CONFORMATION_REGIONS;

    for (const [region, weight] of Object.entries(regions)) {
      const regionScore = conformationScores[region] || 20; // Default to 20 if missing
      totalScore += regionScore * weight;
    }

    return Math.round(totalScore * 10) / 10; // Round to 1 decimal place
  } catch (error) {
    logger.error(`[conformationShowService] Error calculating conformation score: ${error.message}`);
    return 20;
  }
}

/**
 * Calculate handler effectiveness for conformation shows
 * @param {Object} groom - Groom object
 * @param {Object} horse - Horse object
 * @param {string} discipline - Conformation discipline
 * @returns {Object} Handler effectiveness calculation
 */
export function calculateHandlerEffectiveness(groom, horse, discipline) {
  try {
    const skillConfig = CONFORMATION_SHOW_CONFIG.HANDLER_SKILL_BONUSES[groom.skillLevel] || 
                       CONFORMATION_SHOW_CONFIG.HANDLER_SKILL_BONUSES.novice;

    // Base effectiveness from skill level
    let effectiveness = skillConfig.base;

    // Experience bonus (up to max effectiveness)
    const experienceBonus = Math.min(
      (groom.experience || 0) * 0.01, // 1% per experience point
      skillConfig.max - skillConfig.base
    );
    effectiveness += experienceBonus;

    // Specialty bonus for show handling
    if (groom.speciality === 'showHandling') {
      effectiveness += 0.05; // 5% bonus
    }

    // Cap at maximum for skill level
    effectiveness = Math.min(effectiveness, skillConfig.max);

    return {
      effectiveness,
      baseEffectiveness: skillConfig.base,
      experienceBonus,
      specialtyBonus: groom.speciality === 'showHandling' ? 0.05 : 0,
      maxPossible: skillConfig.max
    };

  } catch (error) {
    logger.error(`[conformationShowService] Error calculating handler effectiveness: ${error.message}`);
    return {
      effectiveness: 0.70,
      baseEffectiveness: 0.70,
      experienceBonus: 0,
      specialtyBonus: 0,
      maxPossible: 0.85
    };
  }
}

/**
 * Calculate bond and temperament bonus
 * @param {Object} horse - Horse object with bondScore and temperament
 * @param {Object} groom - Groom object with personality
 * @returns {Object} Bond bonus calculation
 */
export function calculateBondBonus(horse, groom) {
  try {
    let bondBonus = 0;
    const breakdown = {
      bondScore: 0,
      temperamentSynergy: 0,
      total: 0
    };

    // Bond score contribution (0-100 scale converted to 0-1 multiplier)
    const bondScore = horse.bondScore || 0;
    breakdown.bondScore = Math.min(bondScore / 100, 1.0);

    // Temperament-personality synergy
    const temperament = horse.temperament || 'calm';
    const personality = groom.personality || 'gentle';
    const synergyConfig = CONFORMATION_SHOW_CONFIG.TEMPERAMENT_SYNERGY[temperament];

    if (synergyConfig) {
      if (synergyConfig.beneficial.includes(personality)) {
        breakdown.temperamentSynergy = synergyConfig.bonus;
      } else if (synergyConfig.detrimental.includes(personality)) {
        breakdown.temperamentSynergy = -synergyConfig.bonus * 0.5; // Half penalty
      }
    }

    // Calculate total bond bonus
    breakdown.total = breakdown.bondScore + breakdown.temperamentSynergy;
    bondBonus = Math.max(0, Math.min(breakdown.total, 1.5)); // Cap at 150%

    return {
      bondBonus,
      breakdown
    };

  } catch (error) {
    logger.error(`[conformationShowService] Error calculating bond bonus: ${error.message}`);
    return {
      bondBonus: 0.5,
      breakdown: { bondScore: 0.5, temperamentSynergy: 0, total: 0.5 }
    };
  }
}

/**
 * Calculate final conformation show score
 * @param {Object} horse - Horse object with conformation scores
 * @param {Object} groom - Groom handler object
 * @param {string} discipline - Conformation discipline
 * @returns {Object} Complete scoring breakdown
 */
export function calculateConformationShowScore(horse, groom, discipline) {
  try {
    if (!isConformationShow(discipline)) {
      throw new Error(`${discipline} is not a conformation show discipline`);
    }

    // 1. Calculate conformation component (60-70%)
    const conformationScore = calculateConformationScore(horse.conformationScores);
    const conformationComponent = conformationScore * CONFORMATION_SHOW_CONFIG.CONFORMATION_WEIGHT;

    // 2. Calculate handler component (15-25%)
    const handlerEffectiveness = calculateHandlerEffectiveness(groom, horse, discipline);
    const handlerComponent = 100 * handlerEffectiveness.effectiveness * CONFORMATION_SHOW_CONFIG.HANDLER_WEIGHT;

    // 3. Calculate bond component (10-15%)
    const bondCalculation = calculateBondBonus(horse, groom);
    const bondComponent = 100 * bondCalculation.bondBonus * CONFORMATION_SHOW_CONFIG.BOND_WEIGHT;

    // 4. Calculate final score
    const baseScore = conformationComponent + handlerComponent + bondComponent;
    
    // Add small random factor for competition variability (±2%)
    const randomFactor = 0.96 + (Math.random() * 0.08); // 0.96 to 1.04
    const finalScore = Math.max(0, Math.min(100, baseScore * randomFactor));

    logger.info(`[conformationShowService] Conformation show score calculated: ${finalScore.toFixed(1)} for ${horse.name || horse.id} with handler ${groom.name}`);

    return {
      finalScore: Math.round(finalScore * 10) / 10,
      breakdown: {
        conformationScore,
        conformationComponent: Math.round(conformationComponent * 10) / 10,
        handlerComponent: Math.round(handlerComponent * 10) / 10,
        bondComponent: Math.round(bondComponent * 10) / 10,
        randomFactor: Math.round(randomFactor * 1000) / 1000
      },
      handlerEffectiveness,
      bondCalculation,
      weights: {
        conformation: CONFORMATION_SHOW_CONFIG.CONFORMATION_WEIGHT,
        handler: CONFORMATION_SHOW_CONFIG.HANDLER_WEIGHT,
        bond: CONFORMATION_SHOW_CONFIG.BOND_WEIGHT
      }
    };

  } catch (error) {
    logger.error(`[conformationShowService] Error calculating conformation show score: ${error.message}`);
    return {
      finalScore: 0,
      breakdown: {
        conformationScore: 0,
        conformationComponent: 0,
        handlerComponent: 0,
        bondComponent: 0,
        randomFactor: 1.0
      },
      handlerEffectiveness: { effectiveness: 0 },
      bondCalculation: { bondBonus: 0 },
      weights: CONFORMATION_SHOW_CONFIG
    };
  }
}

/**
 * Validate conformation show entry requirements
 * @param {Object} horse - Horse object
 * @param {Object} groom - Groom handler object
 * @param {string} discipline - Conformation discipline
 * @param {string} userId - User ID
 * @returns {Object} Validation result
 */
export async function validateConformationEntry(horse, groom, discipline, userId) {
  try {
    const errors = [];
    const warnings = [];

    // Check if discipline is conformation show
    if (!isConformationShow(discipline)) {
      errors.push(`${discipline} is not a conformation show discipline`);
    }

    // Check horse ownership
    if (horse.ownerId !== userId) {
      errors.push('You do not own this horse');
    }

    // Check groom ownership and assignment
    if (groom.userId !== userId) {
      errors.push('You do not own this groom');
    }

    // Check if groom is assigned to horse
    const assignment = await prisma.groomAssignment.findFirst({
      where: {
        groomId: groom.id,
        foalId: horse.id,
        userId,
        isActive: true
      }
    });

    if (!assignment) {
      errors.push('Groom must be assigned to this horse before entering conformation shows');
    }

    // Check horse age (conformation shows typically for 2+ years)
    const age = horse.age || 0;
    if (age < 2) {
      errors.push('Horse must be at least 2 years old for conformation shows');
    }

    // Check conformation scores exist
    if (!horse.conformationScores) {
      warnings.push('Horse has no conformation scores - default scores will be used');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      assignment
    };

  } catch (error) {
    logger.error(`[conformationShowService] Error validating conformation entry: ${error.message}`);
    return {
      valid: false,
      errors: ['Validation error occurred'],
      warnings: [],
      assignment: null
    };
  }
}
