<!doctype html>
<html lang="en">
  <head>
    <title>Code coverage report for backend/examples</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
      .coverage-summary .sorter {
        background-image: url(../../sort-arrow-sprite.png);
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="pad1">
        <h1><a href="../../index.html">All files</a> backend/examples</h1>
        <div class="clearfix">
          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Statements</span>
            <span class="fraction">0/670</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Branches</span>
            <span class="fraction">0/148</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Functions</span>
            <span class="fraction">0/41</span>
          </div>

          <div class="fl pad1y space-right2">
            <span class="strong">0% </span>
            <span class="quiet">Lines</span>
            <span class="fraction">0/658</span>
          </div>
        </div>
        <p class="quiet">
          Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>,
          <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
          <div class="quiet">
            Filter:
            <input type="search" id="fileSearch" />
          </div>
        </template>
      </div>
      <div class="status-line low"></div>
      <div class="pad1">
        <table class="coverage-summary">
          <thead>
            <tr>
              <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
              <th
                data-col="pic"
                data-type="number"
                data-fmt="html"
                data-html="true"
                class="pic"
              ></th>
              <th data-col="statements" data-type="number" data-fmt="pct" class="pct">
                Statements
              </th>
              <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
              <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
              <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
              <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
              <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="file low" data-value="applyEpigeneticTraitsAtBirthExample.js">
                <a href="applyEpigeneticTraitsAtBirthExample.js.html"
                  >applyEpigeneticTraitsAtBirthExample.js</a
                >
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="65" class="abs low">0/65</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="65" class="abs low">0/65</td>
            </tr>

            <tr>
              <td class="file low" data-value="atBirthTraitsExample.js">
                <a href="atBirthTraitsExample.js.html">atBirthTraitsExample.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="110" class="abs low">0/110</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="44" class="abs low">0/44</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="12" class="abs low">0/12</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="104" class="abs low">0/104</td>
            </tr>

            <tr>
              <td class="file low" data-value="epigeneticTraitsExample.js">
                <a href="epigeneticTraitsExample.js.html">epigeneticTraitsExample.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="122" class="abs low">0/122</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="13" class="abs low">0/13</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="5" class="abs low">0/5</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="120" class="abs low">0/120</td>
            </tr>

            <tr>
              <td class="file low" data-value="foalEnrichmentDemo.js">
                <a href="foalEnrichmentDemo.js.html">foalEnrichmentDemo.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="63" class="abs low">0/63</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="12" class="abs low">0/12</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="5" class="abs low">0/5</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="62" class="abs low">0/62</td>
            </tr>

            <tr>
              <td class="file low" data-value="horseTraitHelperUsage.js">
                <a href="horseTraitHelperUsage.js.html">horseTraitHelperUsage.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="126" class="abs low">0/126</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="49" class="abs low">0/49</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="4" class="abs low">0/4</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="126" class="abs low">0/126</td>
            </tr>

            <tr>
              <td class="file low" data-value="lineageTraitCheckExample.js">
                <a href="lineageTraitCheckExample.js.html">lineageTraitCheckExample.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="107" class="abs low">0/107</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="22" class="abs low">0/22</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="15" class="abs low">0/15</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="104" class="abs low">0/104</td>
            </tr>

            <tr>
              <td class="file low" data-value="traitIntegrationExample.js">
                <a href="traitIntegrationExample.js.html">traitIntegrationExample.js</a>
              </td>
              <td data-value="0" class="pic low">
                <div class="chart">
                  <div class="cover-fill" style="width: 0%"></div>
                  <div class="cover-empty" style="width: 100%"></div>
                </div>
              </td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="77" class="abs low">0/77</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="8" class="abs low">0/8</td>
              <td data-value="100" class="pct high">100%</td>
              <td data-value="0" class="abs high">0/0</td>
              <td data-value="0" class="pct low">0%</td>
              <td data-value="77" class="abs low">0/77</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="push"></div>
      <!-- for sticky footer -->
    </div>
    <!-- /wrapper -->
    <div class="footer quiet pad2 space-top1 center small">
      Code coverage generated by
      <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
      at 2025-05-29T19:19:54.398Z
    </div>
    <script src="../../prettify.js"></script>
    <script>
      window.onload = function () {
        prettyPrint();
      };
    </script>
    <script src="../../sorter.js"></script>
    <script src="../../block-navigation.js"></script>
  </body>
</html>
